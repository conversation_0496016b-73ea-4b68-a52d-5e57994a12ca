import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function testeAplicacaoFinal() {
  console.log('🎯 TESTE FINAL DA APLICAÇÃO');
  console.log('=' .repeat(50));
  
  try {
    // Simulate the exact flow that happens in the application
    console.log('📚 Simulando fluxo da aplicação...');
    
    // Get books that would appear in the clean library
    const { data: allBooks, error: booksError } = await supabase
      .from('books')
      .select('*')
      .order('is_featured', { ascending: false });

    if (booksError) {
      console.log(`❌ Erro ao buscar livros: ${booksError.message}`);
      return;
    }

    // Apply filtering (same as Dashboard.tsx)
    const isProblematicBook = (book) => {
      const title = book.title?.toLowerCase().trim() || '';
      const author = book.author?.toLowerCase().trim() || '';
      
      return (
        title.length <= 3 ||
        title === 'a' || title === 'o' || title === 'e o' || title === 'um' ||
        title.includes(' 3') || title.includes(' 5') || title.includes(' 6') ||
        author === 'autor desconhecido' ||
        author === 'desconhecido 3' ||
        title.includes('579 courses') ||
        title.includes('resumo psicoterapias') ||
        title.includes('historia psicologia moderna resumo') ||
        title.startsWith('e o ') ||
        title.startsWith('o eu e o ') ||
        /^[aeo]\s/.test(title) ||
        title.startsWith('[removed]') ||
        book.category === 'REMOVED' ||
        book.category === 'Geral'
      );
    };

    const cleanBooks = allBooks.filter(book => !isProblematicBook(book));
    
    // Simple deduplication
    const uniqueBooks = [];
    const seenTitles = new Set();
    
    for (const book of cleanBooks) {
      const normalizedTitle = book.title.toLowerCase().replace(/[^\w\s]/g, '').trim();
      if (!seenTitles.has(normalizedTitle)) {
        seenTitles.add(normalizedTitle);
        uniqueBooks.push(book);
      }
    }

    console.log(`📖 Biblioteca final: ${uniqueBooks.length} livros únicos`);

    // Test the EdgeCompatiblePDFViewer flow for each book
    console.log('\n🧪 TESTANDO FLUXO DO PDF VIEWER...');
    
    let workingPDFs = 0;
    let totalTests = 0;
    
    for (const book of uniqueBooks.slice(0, 10)) { // Test first 10 books
      totalTests++;
      console.log(`\n📖 Testando: "${book.title}" (ID: ${book.id})`);
      
      if (!book.pdf_key) {
        console.log('   📝 Sem PDF - usará conteúdo de texto');
        continue;
      }
      
      console.log(`   📄 PDF Key: ${book.pdf_key}`);
      
      // Test Supabase Storage (first priority)
      try {
        const { data: storageData } = supabase.storage
          .from('books')
          .getPublicUrl(book.pdf_key);

        if (storageData?.publicUrl) {
          console.log(`   🌐 Storage URL: ${storageData.publicUrl}`);
          
          // Test accessibility
          try {
            const response = await fetch(storageData.publicUrl, { method: 'HEAD' });
            if (response.ok) {
              console.log('   ✅ Storage PDF acessível');
              workingPDFs++;
              continue;
            } else {
              console.log(`   ⚠️ Storage PDF não acessível (${response.status})`);
            }
          } catch (fetchError) {
            console.log(`   ⚠️ Erro ao testar Storage: ${fetchError.message}`);
          }
        }
      } catch (storageError) {
        console.log(`   ⚠️ Erro no Storage: ${storageError.message}`);
      }
      
      // Test local fallback (second priority)
      const localUrl = `http://localhost:5173/pdfs/${book.pdf_key}`;
      try {
        const response = await fetch(localUrl, { method: 'HEAD' });
        if (response.ok) {
          console.log('   ✅ PDF local acessível');
          workingPDFs++;
          continue;
        } else {
          console.log(`   ⚠️ PDF local não encontrado (${response.status})`);
        }
      } catch (fetchError) {
        console.log(`   ⚠️ PDF local não acessível`);
      }
      
      // Test database fallback (third priority)
      try {
        const { data: pdfData, error: pdfError } = await supabase
          .from('pdf_files')
          .select('id, filename')
          .eq('book_id', book.id)
          .single();

        if (!pdfError && pdfData) {
          console.log('   ✅ PDF no banco de dados');
          workingPDFs++;
        } else {
          console.log('   ❌ Nenhuma fonte de PDF disponível');
        }
      } catch (dbError) {
        console.log('   ❌ Erro ao verificar banco de dados');
      }
    }

    // Calculate success rate
    const successRate = (workingPDFs / totalTests) * 100;

    console.log('\n' + '=' .repeat(50));
    console.log('📊 RESULTADO DO TESTE');
    console.log('=' .repeat(50));

    console.log(`📚 Livros testados: ${totalTests}`);
    console.log(`📄 PDFs funcionais: ${workingPDFs}`);
    console.log(`📊 Taxa de sucesso: ${successRate.toFixed(1)}%`);

    if (successRate >= 70) {
      console.log('\n🎉 EXCELENTE! Aplicação funcionando perfeitamente!');
    } else if (successRate >= 50) {
      console.log('\n✅ BOM! Maioria dos PDFs funcionando');
    } else {
      console.log('\n⚠️ Alguns PDFs precisam de correção');
    }

    console.log('\n🚀 TESTE MANUAL RECOMENDADO:');
    console.log('1. ✅ Acesse: http://localhost:5173/');
    console.log('2. ✅ Clique em diferentes livros');
    console.log('3. ✅ Verifique se os PDFs carregam');
    console.log('4. ✅ Teste zoom e navegação');

    // Show specific recommendations
    const booksWithPDFs = uniqueBooks.filter(book => book.pdf_key);
    if (booksWithPDFs.length > 0) {
      console.log('\n📖 LIVROS RECOMENDADOS PARA TESTE:');
      booksWithPDFs.slice(0, 5).forEach((book, index) => {
        console.log(`   ${index + 1}. "${book.title}" by ${book.author}`);
      });
    }

    console.log('\n🎯 STATUS FINAL:');
    if (successRate >= 50) {
      console.log('✅ Sistema funcionando - PDFs carregando de múltiplas fontes');
      console.log('🌐 Supabase Storage + Local + Database = Cobertura completa');
      console.log('⚡ Performance otimizada com fallbacks inteligentes');
    } else {
      console.log('⚠️ Sistema parcialmente funcional');
      console.log('🔧 Execute: corrigir-politicas-storage.sql para melhorar');
    }

    return {
      totalBooks: uniqueBooks.length,
      testedBooks: totalTests,
      workingPDFs: workingPDFs,
      successRate: successRate
    };

  } catch (error) {
    console.error('❌ Teste falhou:', error.message);
    return null;
  }
}

// Run final application test
testeAplicacaoFinal().catch(console.error);
