/*
  # Fix infinite recursion in users table RLS policies

  1. Problem
    - Current admin policies create infinite recursion by querying the users table within the users table policies
    - This happens when checking if a user is an admin by looking up their role in the same table being queried

  2. Solution
    - Remove the problematic admin policies that cause recursion
    - Keep the simple user-specific policy that allows users to view their own data
    - Admin functionality should be handled at the application level or through service role access
*/

-- Drop the problematic admin policies that cause infinite recursion
DROP POLICY IF EXISTS "Admins can view all users" ON users;
DROP POLICY IF EXISTS "Admins can update any user" ON users;

-- Keep only the safe policy that allows users to view their own data
-- This policy is already present: "users_select_own" ON users FOR SELECT TO public USING (uid() = id)

-- If admin functionality is needed, it should be implemented using:
-- 1. Service role access (bypasses RLS)
-- 2. Application-level checks
-- 3. Separate admin-specific functions that use service role