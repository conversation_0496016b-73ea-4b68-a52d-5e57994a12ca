const https = require('https');

console.log('🔍 TESTE DETALHADO DA STRIPE-PORTAL FUNCTION');
console.log('============================================\n');

const PORTAL_URL = 'https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';

function makeRequest(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname,
      method: method,
      headers: {
        'Authorization': `Bearer ${ANON_KEY}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Node.js Test Client'
      }
    };

    if (data && method === 'POST') {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: responseData
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data && method === 'POST') {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testPortalFunction() {
  console.log('📊 Status da Function:');
  console.log('======================');
  
  // Teste 1: Verificar se existe
  try {
    const getResponse = await makeRequest(PORTAL_URL, 'GET');
    console.log(`✅ Function existe - Status GET: ${getResponse.statusCode}`);
    
    if (getResponse.statusCode === 404) {
      console.log('❌ Function NÃO deployada');
      return;
    }
  } catch (error) {
    console.log('❌ Erro ao acessar function:', error.message);
    return;
  }

  console.log('\n📋 Teste de Parâmetros:');
  console.log('=======================');
  
  // Teste 2: POST sem dados
  try {
    const emptyResponse = await makeRequest(PORTAL_URL, 'POST', {});
    console.log(`📊 POST vazio - Status: ${emptyResponse.statusCode}`);
    console.log(`📄 Response: "${emptyResponse.body}"`);
    
    // Verificar se é nossa function (deve retornar JSON com "Missing customer_id parameter")
    if (emptyResponse.body.includes('Customer ID é obrigatório')) {
      console.log('⚠️  VERSÃO ANTIGA DEPLOYADA (mensagem em português)');
      console.log('   Nossa function retorna: "Missing customer_id parameter"');
      console.log('   Function deployada retorna: "Customer ID é obrigatório"');
    } else if (emptyResponse.body.includes('Missing customer_id parameter')) {
      console.log('✅ VERSÃO CORRETA DEPLOYADA (mensagem em inglês)');
    }
  } catch (error) {
    console.log('❌ Erro no teste de parâmetros:', error.message);
  }

  console.log('\n🧪 Teste com Customer ID Inválido:');
  console.log('==================================');
  
  // Teste 3: POST com customer_id inválido
  try {
    const testData = {
      customer_id: 'cus_test_invalid',
      return_url: 'http://localhost:5173/subscription'
    };
    
    const testResponse = await makeRequest(PORTAL_URL, 'POST', testData);
    console.log(`📊 POST com dados - Status: ${testResponse.statusCode}`);
    console.log(`📄 Response: ${testResponse.body}`);
    
    if (testResponse.statusCode === 500) {
      try {
        const errorData = JSON.parse(testResponse.body);
        if (errorData.error && errorData.error.includes('No such customer')) {
          console.log('✅ Function está funcionando corretamente!');
          console.log('   Erro do Stripe: Customer não existe (esperado)');
        }
      } catch (parseError) {
        console.log('⚠️  Response não é JSON válido');
      }
    }
  } catch (error) {
    console.log('❌ Erro no teste com customer ID:', error.message);
  }

  console.log('\n🔧 Teste CORS:');
  console.log('==============');
  
  // Teste 4: OPTIONS request (CORS)
  try {
    const optionsResponse = await makeRequest(PORTAL_URL, 'OPTIONS');
    console.log(`📊 OPTIONS - Status: ${optionsResponse.statusCode}`);
    console.log(`📄 CORS Headers: ${JSON.stringify(optionsResponse.headers['access-control-allow-origin'] || 'N/A')}`);
    
    if (optionsResponse.statusCode === 200 && optionsResponse.headers['access-control-allow-origin']) {
      console.log('✅ CORS configurado corretamente');
    } else {
      console.log('⚠️  CORS pode ter problemas');
    }
  } catch (error) {
    console.log('❌ Erro no teste CORS:', error.message);
  }
}

async function checkAllFunctions() {
  console.log('\n🔍 Status de Todas as Functions:');
  console.log('================================');
  
  const functions = [
    'stripe-checkout',
    'stripe-webhook', 
    'stripe-portal'
  ];
  
  for (const func of functions) {
    try {
      const url = `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/${func}`;
      const response = await makeRequest(url, 'GET');
      
      if (response.statusCode === 404) {
        console.log(`❌ ${func}: NÃO deployada`);
      } else if (response.statusCode === 405) {
        console.log(`✅ ${func}: Deployada e funcionando`);
      } else {
        console.log(`⚠️  ${func}: Status ${response.statusCode}`);
      }
    } catch (error) {
      console.log(`❌ ${func}: Erro - ${error.message}`);
    }
  }
}

async function main() {
  await testPortalFunction();
  await checkAllFunctions();
  
  console.log('\n🎯 CONCLUSÃO:');
  console.log('=============');
  console.log('✅ stripe-portal function ESTÁ DEPLOYADA');
  console.log('⚠️  Mas pode ser uma versão antiga');
  console.log('');
  console.log('📋 Para garantir que está atualizada:');
  console.log('   npx supabase functions deploy stripe-portal');
  console.log('');
  console.log('🔗 URL da function:');
  console.log(`   ${PORTAL_URL}`);
}

main().catch(console.error);
