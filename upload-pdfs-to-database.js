import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Supabase configuration
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

// PDF folder path
const PDF_FOLDER = path.join(__dirname, 'resumos_padronizados_roboto_final', 'home', 'ubuntu', 'resumos_padronizados', 'pdf_final');

class PDFDatabaseUploader {
  constructor() {
    this.uploadedCount = 0;
    this.errorCount = 0;
    this.errors = [];
  }

  async uploadAllPDFs() {
    console.log('🚀 Starting PDF upload to database...');
    console.log(`📁 PDF folder: ${PDF_FOLDER}`);
    
    try {
      // Check if PDF folder exists
      if (!fs.existsSync(PDF_FOLDER)) {
        throw new Error(`PDF folder not found: ${PDF_FOLDER}`);
      }

      // Get all PDF files
      const pdfFiles = fs.readdirSync(PDF_FOLDER).filter(file => file.endsWith('.pdf'));
      console.log(`📁 Found ${pdfFiles.length} PDF files`);

      // Get all books from database to match PDFs
      const { data: books, error: booksError } = await supabase
        .from('books')
        .select('*')
        .order('id');

      if (booksError) {
        throw new Error(`Failed to fetch books: ${booksError.message}`);
      }

      console.log(`📚 Found ${books.length} books in database`);

      // Process each PDF file
      for (const pdfFile of pdfFiles) {
        await this.processPDFFile(pdfFile, books);
        
        // Small delay to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      console.log('\n📊 UPLOAD SUMMARY:');
      console.log(`✅ Successfully uploaded: ${this.uploadedCount} PDFs`);
      console.log(`❌ Errors: ${this.errorCount} PDFs`);

      if (this.errors.length > 0) {
        console.log('\n⚠️ Errors encountered:');
        this.errors.forEach(error => {
          console.log(`   - ${error}`);
        });
      }

      console.log('\n🎉 PDF upload completed!');
      console.log('📱 PDFs are now accessible globally via database');

      return {
        uploaded: this.uploadedCount,
        errors: this.errorCount,
        total: pdfFiles.length
      };

    } catch (error) {
      console.error('❌ Upload failed:', error.message);
      throw error;
    }
  }

  async processPDFFile(pdfFile, books) {
    try {
      console.log(`\n📄 Processing: ${pdfFile}`);
      
      // Find matching book
      const matchingBook = books.find(book => book.pdf_key === pdfFile);
      
      if (!matchingBook) {
        console.log(`   ⚠️ No matching book found for: ${pdfFile}`);
        this.errors.push(`No matching book found for: ${pdfFile}`);
        this.errorCount++;
        return;
      }

      console.log(`   📚 Matched with book: "${matchingBook.title}" (ID: ${matchingBook.id})`);

      // Check if PDF already exists in database
      const { data: existingPDF } = await supabase
        .from('pdf_files')
        .select('id')
        .eq('book_id', matchingBook.id)
        .single();

      if (existingPDF) {
        console.log(`   ✅ PDF already exists in database, skipping`);
        return;
      }

      // Read PDF file
      const pdfPath = path.join(PDF_FOLDER, pdfFile);
      const pdfBuffer = fs.readFileSync(pdfPath);
      const fileSizeKB = Math.round(pdfBuffer.length / 1024);
      
      console.log(`   📊 File size: ${fileSizeKB} KB`);

      // Convert buffer to base64 for database storage
      const base64Data = pdfBuffer.toString('base64');

      // Insert PDF into database
      console.log(`   ⬆️ Uploading to database...`);
      const { data: pdfData, error: insertError } = await supabase
        .from('pdf_files')
        .insert({
          book_id: matchingBook.id,
          filename: pdfFile,
          file_data: base64Data,
          file_size: pdfBuffer.length,
          mime_type: 'application/pdf'
        })
        .select()
        .single();

      if (insertError) {
        console.log(`   ❌ Database insert failed: ${insertError.message}`);
        this.errors.push(`Database insert failed for ${pdfFile}: ${insertError.message}`);
        this.errorCount++;
        return;
      }

      console.log(`   ✅ Uploaded successfully (PDF ID: ${pdfData.id})`);

      // Update book record to indicate it has a PDF
      const { error: updateError } = await supabase
        .from('books')
        .update({
          has_pdf: true,
          pdf_file_id: pdfData.id
        })
        .eq('id', matchingBook.id);

      if (updateError) {
        console.log(`   ⚠️ Failed to update book record: ${updateError.message}`);
      } else {
        console.log(`   ✅ Book record updated`);
      }

      this.uploadedCount++;

    } catch (error) {
      console.log(`   ❌ Error processing ${pdfFile}: ${error.message}`);
      this.errors.push(`Error processing ${pdfFile}: ${error.message}`);
      this.errorCount++;
    }
  }
}

// Run the upload
const uploader = new PDFDatabaseUploader();
uploader.uploadAllPDFs().catch(console.error);
