import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0'
import Stripe from 'https://esm.sh/stripe@14.0.0?target=deno'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
)

serve(async (req) => {
  const signature = req.headers.get('stripe-signature')
  const body = await req.text()
  
  if (!signature) {
    return new Response('No signature', { status: 400 })
  }

  try {
    // Verify webhook signature
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      Deno.env.get('STRIPE_WEBHOOK_SECRET') || ''
    )

    console.log('Stripe webhook event:', event.type)

    switch (event.type) {
      // Checkout Events
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session)
        break
      case 'checkout.session.expired':
        await handleCheckoutExpired(event.data.object as Stripe.Checkout.Session)
        break

      // Subscription Events
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription)
        break
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break
      case 'customer.subscription.paused':
        await handleSubscriptionPaused(event.data.object as Stripe.Subscription)
        break
      case 'customer.subscription.resumed':
        await handleSubscriptionResumed(event.data.object as Stripe.Subscription)
        break
      case 'customer.subscription.trial_will_end':
        await handleTrialWillEnd(event.data.object as Stripe.Subscription)
        break

      // Invoice Events
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice)
        break
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice)
        break
      case 'invoice.created':
        await handleInvoiceCreated(event.data.object as Stripe.Invoice)
        break
      case 'invoice.finalized':
        await handleInvoiceFinalized(event.data.object as Stripe.Invoice)
        break
      case 'invoice.payment_action_required':
        await handlePaymentActionRequired(event.data.object as Stripe.Invoice)
        break

      // Customer Events
      case 'customer.created':
        await handleCustomerCreated(event.data.object as Stripe.Customer)
        break
      case 'customer.updated':
        await handleCustomerUpdated(event.data.object as Stripe.Customer)
        break
      case 'customer.deleted':
        await handleCustomerDeleted(event.data.object as Stripe.Customer)
        break

      // Payment Events
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent)
        break
      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent)
        break

      // Charge Events
      case 'charge.succeeded':
        await handleChargeSucceeded(event.data.object as Stripe.Charge)
        break
      case 'charge.failed':
        await handleChargeFailed(event.data.object as Stripe.Charge)
        break
      case 'charge.refunded':
        await handleChargeRefunded(event.data.object as Stripe.Charge)
        break

      // Dispute Events
      case 'charge.dispute.created':
        await handleDisputeCreated(event.data.object as Stripe.Dispute)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
        // Log unhandled events for monitoring
        await logUnhandledEvent(event)
    }

    return new Response('OK', { status: 200 })

  } catch (error) {
    console.error('Webhook error:', error)
    return new Response(`Webhook error: ${error.message}`, { status: 400 })
  }
})

async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  console.log('Checkout completed:', session.id)
  
  const userId = session.metadata?.user_id
  const planId = session.metadata?.plan_id
  
  if (!userId) {
    console.error('No user_id in session metadata')
    return
  }

  // Update user subscription status
  await supabaseClient
    .from('user_subscriptions')
    .update({
      subscription_type: planId || 'premium',
      status: 'active',
      stripe_subscription_id: session.subscription as string,
      stripe_customer_id: session.customer as string,
      started_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  console.log('Subscription created:', subscription.id)
  
  const userId = subscription.metadata?.user_id
  const planId = subscription.metadata?.plan_id
  
  if (!userId) {
    console.error('No user_id in subscription metadata')
    return
  }

  const currentPeriodEnd = new Date(subscription.current_period_end * 1000).toISOString()

  await supabaseClient
    .from('user_subscriptions')
    .update({
      subscription_type: planId || 'premium',
      status: subscription.status,
      stripe_subscription_id: subscription.id,
      expires_at: currentPeriodEnd,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log('Subscription updated:', subscription.id)
  
  const userId = subscription.metadata?.user_id
  
  if (!userId) {
    // Try to find user by customer ID
    const { data: userSub } = await supabaseClient
      .from('user_subscriptions')
      .select('user_id')
      .eq('stripe_customer_id', subscription.customer as string)
      .single()
    
    if (!userSub) {
      console.error('Could not find user for subscription:', subscription.id)
      return
    }
  }

  const currentPeriodEnd = new Date(subscription.current_period_end * 1000).toISOString()

  await supabaseClient
    .from('user_subscriptions')
    .update({
      status: subscription.status,
      expires_at: currentPeriodEnd,
      updated_at: new Date().toISOString()
    })
    .eq('stripe_subscription_id', subscription.id)
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log('Subscription deleted:', subscription.id)
  
  await supabaseClient
    .from('user_subscriptions')
    .update({
      subscription_type: 'free',
      status: 'cancelled',
      expires_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      remaining_free_access: 5 // Reset free access
    })
    .eq('stripe_subscription_id', subscription.id)
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log('Payment succeeded:', invoice.id)
  
  if (invoice.subscription) {
    // Update subscription status to active
    await supabaseClient
      .from('user_subscriptions')
      .update({
        status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('stripe_subscription_id', invoice.subscription as string)
  }
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  console.log('Payment failed:', invoice.id)

  if (invoice.subscription) {
    // Update subscription status to past_due
    await supabaseClient
      .from('user_subscriptions')
      .update({
        status: 'past_due',
        updated_at: new Date().toISOString()
      })
      .eq('stripe_subscription_id', invoice.subscription as string)
  }
}

// Additional Event Handlers
async function handleCheckoutExpired(session: Stripe.Checkout.Session) {
  console.log('Checkout session expired:', session.id)
  // Log expired sessions for analytics
}

async function handleSubscriptionPaused(subscription: Stripe.Subscription) {
  console.log('Subscription paused:', subscription.id)

  await supabaseClient
    .from('user_subscriptions')
    .update({
      status: 'paused',
      updated_at: new Date().toISOString()
    })
    .eq('stripe_subscription_id', subscription.id)
}

async function handleSubscriptionResumed(subscription: Stripe.Subscription) {
  console.log('Subscription resumed:', subscription.id)

  await supabaseClient
    .from('user_subscriptions')
    .update({
      status: 'active',
      updated_at: new Date().toISOString()
    })
    .eq('stripe_subscription_id', subscription.id)
}

async function handleTrialWillEnd(subscription: Stripe.Subscription) {
  console.log('Trial will end:', subscription.id)

  // Send notification to user about trial ending
  // You can implement email notifications here
}

async function handleInvoiceCreated(invoice: Stripe.Invoice) {
  console.log('Invoice created:', invoice.id)
  // Log invoice creation for accounting
}

async function handleInvoiceFinalized(invoice: Stripe.Invoice) {
  console.log('Invoice finalized:', invoice.id)
  // Invoice is ready to be sent to customer
}

async function handlePaymentActionRequired(invoice: Stripe.Invoice) {
  console.log('Payment action required:', invoice.id)

  if (invoice.subscription) {
    await supabaseClient
      .from('user_subscriptions')
      .update({
        status: 'incomplete',
        updated_at: new Date().toISOString()
      })
      .eq('stripe_subscription_id', invoice.subscription as string)
  }
}

async function handleCustomerCreated(customer: Stripe.Customer) {
  console.log('Customer created:', customer.id)
  // Customer data is already handled in checkout completion
}

async function handleCustomerUpdated(customer: Stripe.Customer) {
  console.log('Customer updated:', customer.id)

  // Update customer information in database
  if (customer.metadata?.supabase_user_id) {
    await supabaseClient
      .from('user_subscriptions')
      .update({
        updated_at: new Date().toISOString()
      })
      .eq('user_id', customer.metadata.supabase_user_id)
  }
}

async function handleCustomerDeleted(customer: Stripe.Customer) {
  console.log('Customer deleted:', customer.id)

  // Handle customer deletion
  await supabaseClient
    .from('user_subscriptions')
    .update({
      stripe_customer_id: null,
      updated_at: new Date().toISOString()
    })
    .eq('stripe_customer_id', customer.id)
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment intent succeeded:', paymentIntent.id)
  // Additional payment processing if needed
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment intent failed:', paymentIntent.id)
  // Handle failed payment intents
}

async function handleChargeSucceeded(charge: Stripe.Charge) {
  console.log('Charge succeeded:', charge.id)
  // Log successful charges for accounting
}

async function handleChargeFailed(charge: Stripe.Charge) {
  console.log('Charge failed:', charge.id)
  // Log failed charges
}

async function handleChargeRefunded(charge: Stripe.Charge) {
  console.log('Charge refunded:', charge.id)
  // Handle refunds - might need to update subscription status
}

async function handleDisputeCreated(dispute: Stripe.Dispute) {
  console.log('Dispute created:', dispute.id)
  // Handle disputes - notify admin, update records
}

async function logUnhandledEvent(event: any) {
  console.log('Unhandled event:', event.type, event.id)

  // Log unhandled events to database for monitoring
  try {
    await supabaseClient
      .from('webhook_logs')
      .insert({
        event_type: event.type,
        event_id: event.id,
        processed: false,
        created_at: new Date().toISOString()
      })
  } catch (error) {
    console.error('Failed to log unhandled event:', error)
  }
}
