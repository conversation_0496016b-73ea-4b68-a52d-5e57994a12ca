import React from 'react';
import { motion } from 'framer-motion';
import { Icons } from '@/components/ui/icons';

interface DashboardStatsProps {
  stats: {
    total_summaries_read: number;
    total_reading_time: number;
    favorites_count: number;
    in_progress_count: number;
  };
}

export function DashboardStats({ stats }: DashboardStatsProps) {
  const statItems = [
    {
      label: 'Resumos Lidos',
      value: stats.total_summaries_read,
      icon: Icons.BookOpen,
      color: 'bg-blue-50 text-blue-600',
      bgColor: 'bg-blue-500',
      change: '+2 esta semana',
      changeType: 'positive'
    },
    {
      label: 'Horas de Leitura',
      value: `${Math.round(stats.total_reading_time / 60)}h`,
      icon: Icons.Clock,
      color: 'bg-green-50 text-green-600',
      bgColor: 'bg-green-500',
      change: '+3h esta semana',
      changeType: 'positive'
    },
    {
      label: 'Favoritos',
      value: stats.favorites_count,
      icon: Icons.Star,
      color: 'bg-yellow-50 text-yellow-600',
      bgColor: 'bg-yellow-500',
      change: '+1 novo',
      changeType: 'positive'
    },
    {
      label: 'Em Progresso',
      value: stats.in_progress_count,
      icon: Icons.TrendingUp,
      color: 'bg-purple-50 text-purple-600',
      bgColor: 'bg-purple-500',
      change: '2 pendentes',
      changeType: 'neutral'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statItems.map((item, index) => (
        <motion.div
          key={item.label}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          whileHover={{ y: -4, scale: 1.02 }}
          className="bg-white rounded-2xl border border-gray-100 p-6 hover:shadow-lg transition-all duration-300 group"
        >
          <div className="flex items-start justify-between mb-4">
            <div className={`w-12 h-12 rounded-xl ${item.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
              <item.icon className="w-6 h-6" />
            </div>
            
            <div className={`text-xs px-2 py-1 rounded-full ${
              item.changeType === 'positive' 
                ? 'bg-green-100 text-green-700' 
                : 'bg-gray-100 text-gray-600'
            }`}>
              {item.change}
            </div>
          </div>

          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-500">
              {item.label}
            </p>
            <p className="text-3xl font-bold text-gray-900 group-hover:text-gray-800 transition-colors">
              {item.value}
            </p>
          </div>

          {/* Progress indicator */}
          <div className="mt-4 w-full bg-gray-100 rounded-full h-1">
            <motion.div 
              className={`h-1 rounded-full ${item.bgColor}`}
              initial={{ width: 0 }}
              animate={{ width: `${Math.min(100, (typeof item.value === 'number' ? item.value : 50) * 5)}%` }}
              transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
            />
          </div>
        </motion.div>
      ))}
    </div>
  );
}