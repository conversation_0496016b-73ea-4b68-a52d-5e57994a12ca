# 🔧 DEPLOY DA STRIPE-PORTAL CORRETA

## 🎯 **SITUAÇÃO:**

O comando que você executou criou uma versão em **português**, mas precisamos da versão em **inglês**.

## ✅ **ARQUIVO CORRETO JÁ ESTÁ PRONTO:**

O arquivo `supabase/functions/stripe-portal/index.ts` agora está com a versão correta (inglês).

## 🚀 **OPÇÕES PARA DEPLOY:**

### **OPÇÃO 1 - VIA CLI (RECOMENDADO):**

Execute estes comandos:

```bash
# 1. Login no Supabase
npx supabase login

# 2. Link do projeto (se necessário)
npx supabase link --project-ref qmeelujsnpbcdkzhcwmm

# 3. Deploy da função correta
npx supabase functions deploy stripe-portal
```

### **OPÇÃO 2 - VIA DASHBOARD:**

1. **Acesse:** https://supabase.com/dashboard/project/qmeelujsnpbcdkzhcwmm/functions

2. **Encontre a função "stripe-portal"** e clique para editar

3. **Cole este código completo:**

```typescript
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0'
import Stripe from 'https://esm.sh/stripe@14.0.0?target=deno'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    const { customer_id, return_url } = await req.json()

    if (!customer_id) {
      return new Response(
        JSON.stringify({ error: 'Missing customer_id parameter' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create Stripe customer portal session
    const session = await stripe.billingPortal.sessions.create({
      customer: customer_id,
      return_url: return_url || `${Deno.env.get('SUPABASE_URL')}/subscription`,
    })

    return new Response(
      JSON.stringify({ url: session.url }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error creating portal session:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
```

4. **Salve e faça deploy**

## 🧪 **VERIFICAR SE FUNCIONOU:**

Após o deploy, execute:

```bash
node test-portal-detailed.cjs
```

**Resultado esperado:**
- ✅ `"Missing customer_id parameter"` (inglês) ← **CORRETO**
- ❌ ~~`"Customer ID é obrigatório"`~~ (português) ← **INCORRETO**

## 🎯 **DIFERENÇAS PRINCIPAIS:**

### **Versão Anterior (Incorreta):**
- ❌ Mensagem: `"Customer ID é obrigatório"`
- ❌ Erro: `"Falha ao criar portal do Stripe"`
- ❌ Sem CORS adequado

### **Versão Correta:**
- ✅ Mensagem: `"Missing customer_id parameter"`
- ✅ Erro: `error.message` (detalhado)
- ✅ CORS configurado
- ✅ Supabase client integrado

## 🚀 **APÓS CORREÇÃO:**

**Sua integração estará 100% funcional:**

✅ **stripe-checkout** - Funcionando
✅ **stripe-webhook** - Funcionando  
✅ **stripe-portal** - Funcionando (versão correta)
✅ **Webhook Stripe** - 24 eventos configurados

**URLs das Functions:**
- `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-checkout`
- `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-webhook`
- `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal`

---

## 📋 **EXECUTE AGORA:**

**Escolha uma das opções acima e faça o deploy da versão correta!**

**Depois execute `node test-portal-detailed.cjs` para confirmar que funcionou.**
