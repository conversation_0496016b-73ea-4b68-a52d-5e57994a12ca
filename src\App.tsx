import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Icons } from '@/components/ui/icons';
// import SimpleHeroSection from './components/landing/SimpleHeroSection';
// import { AuthForm } from './components/auth/AuthForm';
// import Dashboard from './components/dashboard/Dashboard';
import { Button } from './components/ui/button';
// import { auth } from './lib/supabase';

function App() {
  const [user, setUser] = useState(null);
  const [showAuth, setShowAuth] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Temporarily disable auth to test
    setLoading(false);

    // // Check if user is already logged in
    // auth.getUser().then(({ data: { user } }) => {
    //   setUser(user);
    //   setLoading(false);
    // });

    // // Listen for auth changes
    // const { data: { subscription } } = auth.onAuthStateChange((event, session) => {
    //   setUser(session?.user || null);
    //   if (session?.user) {
    //     setShowAuth(false);
    //   }
    // });

    // return () => subscription.unsubscribe();
  }, []);

  const handleSignOut = async () => {
    // await auth.signOut();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center w-full max-w-full overflow-hidden">
        <div className="flex flex-col items-center space-y-4">
          <div className="relative">
            <div className="w-12 h-12 border-4 border-gray-200 rounded-full"></div>
            <div className="w-12 h-12 border-4 border-gray-900 border-t-transparent rounded-full animate-spin absolute top-0 left-0"></div>
          </div>
          <span className="text-gray-900 text-lg font-medium">Carregando...</span>
        </div>
      </div>
    );
  }

  if (user) {
    return <div>Dashboard placeholder</div>;
  }

  return (
    <div className="min-h-screen bg-white relative overflow-hidden w-full max-w-full">
      {/* Navigation */}
      <nav className="relative z-20 bg-white/90 backdrop-blur-xl border-b border-gray-100">
        <div className="flex items-center justify-between max-w-7xl mx-auto px-6 py-4">
          <motion.div
            className="flex items-center space-x-3"
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-gray-900 to-gray-700 flex items-center justify-center shadow-elegant">
              <Icons.BookOpen className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900 text-elegant">Paretto Estudos</span>
          </motion.div>
          <Button
            onClick={() => setShowAuth(true)}
            className="interactive-button bg-gray-900 text-white hover:bg-gray-800 px-6 py-2 shadow-elegant"
          >
            Entrar
          </Button>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="flex items-center justify-center min-h-[80vh]">
        <div className="text-center">
          <h1 className="text-6xl font-bold text-gray-900 mb-4">Paretto Estudos</h1>
          <p className="text-xl text-gray-600 mb-8">Acelere seu aprendizado com resumos inteligentes</p>
          <Button
            onClick={() => setShowAuth(true)}
            className="bg-gray-900 text-white hover:bg-gray-800 px-8 py-3 text-lg"
          >
            Começar Agora
          </Button>
        </div>
      </div>

      {/* Auth Modal */}
      {showAuth && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-6 bg-black/30">
          <div className="bg-white p-8 rounded-lg">
            <h2 className="text-2xl font-bold mb-4">Login</h2>
            <p className="mb-4">Formulário de login aqui</p>
            <Button onClick={() => setShowAuth(false)}>Fechar</Button>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;