import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function restoreBackup() {
  console.log('🔄 Restoring library from backup...');
  
  try {
    // Read backup files
    const books = JSON.parse(fs.readFileSync('books.json', 'utf8'));
    const contents = JSON.parse(fs.readFileSync('book_contents.json', 'utf8'));
    
    console.log(`📚 Restoring ${books.length} books...`);
    console.log(`📄 Restoring ${contents.length} book contents...`);
    
    // Clear existing data
    await supabase.from('book_contents').delete().neq('book_id', 0);
    await supabase.from('books').delete().neq('id', 0);
    
    // Restore books
    const { error: booksError } = await supabase
      .from('books')
      .insert(books);
    
    if (booksError) {
      console.error('❌ Error restoring books:', booksError.message);
      return;
    }
    
    // Restore contents
    const { error: contentsError } = await supabase
      .from('book_contents')
      .insert(contents);
    
    if (contentsError) {
      console.error('❌ Error restoring contents:', contentsError.message);
      return;
    }
    
    console.log('✅ Backup restored successfully!');
    
  } catch (error) {
    console.error('❌ Restore failed:', error.message);
  }
}

restoreBackup().catch(console.error);