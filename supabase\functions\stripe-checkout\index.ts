import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0'
import Stripe from 'https://esm.sh/stripe@14.0.0?target=deno'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    const { price_id, user_id, success_url, cancel_url, plan_id } = await req.json()

    if (!price_id || !user_id) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get or create Stripe customer
    let customer_id: string

    // Check if user already has a Stripe customer ID
    const { data: existingUser } = await supabaseClient
      .from('user_subscriptions')
      .select('stripe_customer_id')
      .eq('user_id', user_id)
      .single()

    if (existingUser?.stripe_customer_id) {
      customer_id = existingUser.stripe_customer_id
    } else {
      // Get user email from auth
      const { data: { user } } = await supabaseClient.auth.admin.getUserById(user_id)
      
      if (!user?.email) {
        return new Response(
          JSON.stringify({ error: 'User email not found' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      // Create Stripe customer
      const customer = await stripe.customers.create({
        email: user.email,
        metadata: {
          supabase_user_id: user_id,
        },
      })

      customer_id = customer.id

      // Update user subscription record with Stripe customer ID
      await supabaseClient
        .from('user_subscriptions')
        .upsert({
          user_id: user_id,
          stripe_customer_id: customer_id,
          subscription_type: 'free',
          status: 'active',
          remaining_free_access: 5
        })
    }

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customer_id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: price_id,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: success_url,
      cancel_url: cancel_url,
      metadata: {
        user_id: user_id,
        plan_id: plan_id,
      },
      subscription_data: {
        metadata: {
          user_id: user_id,
          plan_id: plan_id,
        },
      },
    })

    return new Response(
      JSON.stringify({ session_id: session.id }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error creating checkout session:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
