-- Remove specific book summaries from the database
-- Execute this SQL in Supabase Dashboard > SQL Editor

-- STEP 1: First, let's see what books we have that match our criteria
SELECT 'BEFORE REMOVAL - Books to be removed:' as status;

SELECT id, title, author, category, duration, difficulty, is_featured
FROM books
WHERE (LOWER(title) LIKE '%pense e enriqueça%' OR LOWER(title) LIKE '%pense e enriqueca%' OR
       (LOWER(title) LIKE '%pense%' AND LOWER(author) LIKE '%napoleon hill%'))
   OR (LOWER(title) LIKE '%gestalt%terapia%' OR LOWER(title) LIKE '%gestalt-terapia%' OR
       (LOWER(title) LIKE '%gestalt%' AND <PERSON>OWER(author) LIKE '%perls%'))
ORDER BY id;

-- STEP 2: Remove the books and associated data
DO $$
DECLARE
    book_record RECORD;
    removed_count INTEGER := 0;
BEGIN
    -- Loop through all matching books
    FOR book_record IN
        SELECT id, title, author
        FROM books
        WHERE (LOWER(title) LIKE '%pense e enriqueça%' OR LOWER(title) LIKE '%pense e enriqueca%' OR
               (LOWER(title) LIKE '%pense%' AND LOWER(author) LIKE '%napoleon hill%'))
           OR (LOWER(title) LIKE '%gestalt%terapia%' OR LOWER(title) LIKE '%gestalt-terapia%' OR
               (LOWER(title) LIKE '%gestalt%' AND LOWER(author) LIKE '%perls%'))
    LOOP
        RAISE NOTICE 'Processing book ID %, Title: "%", Author: "%"',
            book_record.id, book_record.title, book_record.author;

        -- Remove user progress data for this book
        DELETE FROM user_reading_progress WHERE book_id = book_record.id::TEXT;
        RAISE NOTICE '  → Removed user progress data for book ID: %', book_record.id;

        -- Remove summaries data (if exists)
        DELETE FROM summaries WHERE book_id = book_record.id;
        RAISE NOTICE '  → Removed summaries data for book ID: %', book_record.id;

        -- Remove book contents (if exists)
        DELETE FROM book_contents WHERE book_id = book_record.id;
        RAISE NOTICE '  → Removed book contents for book ID: %', book_record.id;

        -- Finally, remove the book itself
        DELETE FROM books WHERE id = book_record.id;
        RAISE NOTICE '  → Removed book: "%" (ID: %)', book_record.title, book_record.id;

        removed_count := removed_count + 1;
    END LOOP;

    RAISE NOTICE 'REMOVAL COMPLETE: % books removed successfully', removed_count;

END $$;

-- STEP 3: Verification queries - run these to confirm removal
SELECT 'AFTER REMOVAL - Verification Results:' as status;

SELECT 'Books with Napoleon Hill as author' as check_type, COUNT(*) as count
FROM books
WHERE LOWER(author) LIKE '%napoleon hill%'

UNION ALL

SELECT 'Books with Gestalt in title' as check_type, COUNT(*) as count
FROM books
WHERE LOWER(title) LIKE '%gestalt%'

UNION ALL

SELECT 'User progress for removed books' as check_type, COUNT(*) as count
FROM user_reading_progress
WHERE book_id IN (
    SELECT id::TEXT FROM books
    WHERE LOWER(title) LIKE '%pense e enriqueça%'
       OR LOWER(title) LIKE '%gestalt%'
);

-- STEP 4: Additional cleanup - Remove any orphaned data
DELETE FROM summaries
WHERE book_id NOT IN (SELECT id FROM books);

DELETE FROM book_contents
WHERE book_id NOT IN (SELECT id FROM books);

-- STEP 5: Final verification
SELECT 'FINAL STATUS:' as status;
SELECT 'Total books remaining' as info, COUNT(*) as count FROM books;
SELECT 'Total summaries remaining' as info, COUNT(*) as count FROM summaries;
SELECT 'Total book contents remaining' as info, COUNT(*) as count FROM book_contents;

-- Show some remaining books to verify the library is still intact
SELECT 'Sample of remaining books:' as status;
SELECT id, title, author, category, is_featured
FROM books
ORDER BY id
LIMIT 10;
