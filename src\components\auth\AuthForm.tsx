import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Icons } from '@/components/ui/icons';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { auth } from '@/lib/supabase';

interface AuthFormProps {
  onSuccess: () => void;
  onBack?: () => void;
}

export function AuthForm({ onSuccess, onBack }: AuthFormProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('login');

  const handleAuth = async (isLogin: boolean) => {
    setLoading(true);
    setError('');

    try {
      if (isLogin) {
        const { error } = await auth.signIn(email, password);
        if (error) throw error;
      } else {
        const { error } = await auth.signUp(email, password);
        if (error) throw error;
      }
      onSuccess();
    } catch (err: any) {
      console.error('Auth error:', err);

      // Handle specific error cases
      if (err.message?.includes('User already registered') || err.code === 'user_already_exists') {
        setError('Este e-mail já está cadastrado. Por favor, faça login.');
        setTimeout(() => {
          setActiveTab('login');
          setError('');
        }, 3000);
      } else if (err.message?.includes('Invalid login credentials')) {
        setError('E-mail ou senha incorretos. Verifique suas credenciais.');
      } else if (err.message?.includes('Email not confirmed')) {
        setError('Por favor, confirme seu e-mail antes de fazer login.');
      } else if (err.message?.includes('Signup is disabled')) {
        setError('O registro de novos usuários está temporariamente desabilitado. Entre em contato com o suporte.');
      } else if (err.message?.includes('Password should be at least')) {
        setError('A senha deve ter pelo menos 6 caracteres.');
      } else if (err.message?.includes('Unable to validate email address')) {
        setError('Formato de e-mail inválido. Verifique o endereço digitado.');
      } else if (!isLogin && err.message?.includes('Email rate limit exceeded')) {
        setError('Muitas tentativas de registro. Aguarde alguns minutos antes de tentar novamente.');
      } else {
        // Show more detailed error for debugging
        const errorMessage = err.message || 'Ocorreu um erro. Tente novamente.';
        setError(`${errorMessage}${err.code ? ` (${err.code})` : ''}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -30, scale: 0.95 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="w-full max-w-md mx-auto"
    >
      <div className="glass-card shadow-elegant-xl rounded-3xl p-8 backdrop-blur-xl border border-gray-200">
        {/* Back Button */}
        {onBack && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="mb-6"
          >
            <motion.button
              onClick={onBack}
              whileHover={{ x: -2, scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100/80 p-3 rounded-xl transition-all duration-200 group backdrop-blur-sm border border-transparent hover:border-gray-200 hover:shadow-sm"
            >
              <motion.div
                whileHover={{ x: -2 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Icons.ArrowLeft className="w-4 h-4 group-hover:text-gray-900" />
              </motion.div>
              <span className="text-sm font-medium">Voltar à página inicial</span>
            </motion.button>
          </motion.div>
        )}

        {/* Header */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ 
              delay: 0.2, 
              type: "spring", 
              stiffness: 200, 
              damping: 15 
            }}
            className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center shadow-elegant-lg"
          >
            <Icons.BookOpen className="w-8 h-8 text-white" />
          </motion.div>
          
          <motion.h2 
            className="text-2xl font-bold text-gray-900 mb-3 text-elegant"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            Bem-vindo ao Paretto Estudos
          </motion.h2>
          
          <motion.div
            className="flex items-center justify-center space-x-2 text-gray-600 text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <Icons.Sparkles className="w-4 h-4" />
            <span>Aprenda 95% do conhecimento em 20% do tempo</span>
          </motion.div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-gray-100 border-gray-200 mb-6 p-1 rounded-xl">
            <TabsTrigger 
              value="login" 
              className="text-gray-700 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-elegant rounded-lg transition-all duration-200"
            >
              Entrar
            </TabsTrigger>
            <TabsTrigger 
              value="register"
              className="text-gray-700 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-elegant rounded-lg transition-all duration-200"
            >
              Criar Conta
            </TabsTrigger>
          </TabsList>

          <TabsContent value="login" className="space-y-4">
            <AuthFormContent
              email={email}
              setEmail={setEmail}
              password={password}
              setPassword={setPassword}
              showPassword={showPassword}
              setShowPassword={setShowPassword}
              loading={loading}
              error={error}
              onSubmit={() => handleAuth(true)}
              submitText="Entrar"
            />
          </TabsContent>

          <TabsContent value="register" className="space-y-4">
            <AuthFormContent
              email={email}
              setEmail={setEmail}
              password={password}
              setPassword={setPassword}
              showPassword={showPassword}
              setShowPassword={setShowPassword}
              loading={loading}
              error={error}
              onSubmit={() => handleAuth(false)}
              submitText="Criar Conta"
            />
          </TabsContent>
        </Tabs>



        {/* Footer */}
        <motion.div
          className="mt-6 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <p className="text-gray-500 text-xs">
            Ao continuar, você concorda com nossos{' '}
            <a href="#" className="text-gray-700 hover:text-gray-900 underline transition-colors">
              Termos de Uso
            </a>
          </p>
        </motion.div>
      </div>
    </motion.div>
  );
}

interface AuthFormContentProps {
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
  showPassword: boolean;
  setShowPassword: (show: boolean) => void;
  loading: boolean;
  error: string;
  onSubmit: () => void;
  submitText: string;
}

function AuthFormContent({
  email,
  setEmail,
  password,
  setPassword,
  showPassword,
  setShowPassword,
  loading,
  error,
  onSubmit,
  submitText
}: AuthFormContentProps) {
  return (
    <form onSubmit={(e) => { e.preventDefault(); onSubmit(); }} className="space-y-5">
      {error && (
        <motion.div
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          className="p-4 rounded-xl bg-red-50 border border-red-200 text-red-700 text-sm shadow-elegant"
        >
          {error}
        </motion.div>
      )}

      <motion.div 
        className="space-y-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1, duration: 0.5 }}
      >
        <Label htmlFor="email" className="text-gray-700 text-sm font-medium">
          Email
        </Label>
        <div className="relative group">
          <Icons.Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 group-focus-within:text-gray-600 transition-colors" />
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="pl-10 bg-white border-gray-200 text-gray-900 placeholder:text-gray-400 focus:border-gray-400 focus:ring-2 focus:ring-gray-100 transition-all duration-200 rounded-xl"
            placeholder="<EMAIL>"
            required
          />
        </div>
      </motion.div>

      <motion.div 
        className="space-y-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <Label htmlFor="password" className="text-gray-700 text-sm font-medium">
          Senha
        </Label>
        <div className="relative group">
          <Icons.Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 group-focus-within:text-gray-600 transition-colors" />
          <Input
            id="password"
            type={showPassword ? "text" : "password"}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="pl-10 pr-10 bg-white border-gray-200 text-gray-900 placeholder:text-gray-400 focus:border-gray-400 focus:ring-2 focus:ring-gray-100 transition-all duration-200 rounded-xl"
            placeholder="••••••••"
            required
          />
          <motion.button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            {showPassword ? <Icons.EyeOff className="w-4 h-4" /> : <Icons.Eye className="w-4 h-4" />}
          </motion.button>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <Button
          type="submit"
          disabled={loading}
          className="interactive-button w-full h-12 bg-gray-900 hover:bg-gray-800 text-white font-semibold rounded-xl transition-all duration-300 shadow-elegant disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              <span>Carregando...</span>
            </div>
          ) : (
            <motion.div 
              className="flex items-center space-x-2"
              whileHover={{ x: 2 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <span>{submitText}</span>
              <Icons.ArrowRight className="w-4 h-4" />
            </motion.div>
          )}
        </Button>
      </motion.div>
    </form>
  );
}