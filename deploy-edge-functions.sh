#!/bin/bash

echo "🚀 PARETTO - SUPABASE EDGE FUNCTIONS DEPLOYMENT"
echo "==============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_REF="qmeelujsnpbcdkzhcwmm"
STRIPE_SECRET_KEY="sk_test_51RIcDWQXmlk54RXco22WsOUYY2jtE27RhFyvbaaiInFpIDD5yFcbQNPjSilbv6pzKxq1YuihBgAy4ria1OV3l2mY00gw9ED5fy"
STRIPE_WEBHOOK_SECRET="whsec_rSOSlhBwwbkRSVfgqla96EctF9NTcL9c"
SUPABASE_URL="https://qmeelujsnpbcdkzhcwmm.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Supabase CLI is installed
check_supabase_cli() {
    print_info "Checking Supabase CLI installation..."
    
    if ! command -v supabase &> /dev/null; then
        print_error "Supabase CLI not found!"
        echo ""
        echo "Please install Supabase CLI:"
        echo "  npm install -g supabase"
        echo "  or visit: https://supabase.com/docs/guides/cli"
        exit 1
    fi
    
    print_status "Supabase CLI found"
    supabase --version
}

# Login to Supabase
login_supabase() {
    print_info "Checking Supabase authentication..."
    
    # Check if already logged in
    if supabase projects list &> /dev/null; then
        print_status "Already logged in to Supabase"
    else
        print_info "Please login to Supabase..."
        supabase login
    fi
}

# Initialize and link project
setup_project() {
    print_info "Setting up Supabase project..."
    
    # Initialize if not already done
    if [ ! -d "supabase" ]; then
        print_info "Initializing Supabase project..."
        supabase init
    else
        print_status "Supabase project already initialized"
    fi
    
    # Link project
    print_info "Linking to Supabase project..."
    supabase link --project-ref $PROJECT_REF
    
    if [ $? -eq 0 ]; then
        print_status "Project linked successfully"
    else
        print_error "Failed to link project"
        exit 1
    fi
}

# Set secrets
set_secrets() {
    print_info "Setting up Supabase secrets..."
    
    echo "Setting STRIPE_SECRET_KEY..."
    supabase secrets set STRIPE_SECRET_KEY="$STRIPE_SECRET_KEY"
    
    echo "Setting STRIPE_WEBHOOK_SECRET..."
    supabase secrets set STRIPE_WEBHOOK_SECRET="$STRIPE_WEBHOOK_SECRET"
    
    echo "Setting SUPABASE_URL..."
    supabase secrets set SUPABASE_URL="$SUPABASE_URL"
    
    echo "Setting SUPABASE_ANON_KEY..."
    supabase secrets set SUPABASE_ANON_KEY="$SUPABASE_ANON_KEY"
    
    print_status "Secrets configured"
    
    # List secrets to verify
    print_info "Verifying secrets..."
    supabase secrets list
}

# Deploy functions
deploy_functions() {
    print_info "Deploying Edge Functions..."
    
    # Deploy stripe-checkout
    echo ""
    print_info "Deploying stripe-checkout function..."
    supabase functions deploy stripe-checkout
    
    if [ $? -eq 0 ]; then
        print_status "stripe-checkout deployed successfully"
    else
        print_error "Failed to deploy stripe-checkout"
        exit 1
    fi
    
    # Deploy stripe-webhook
    echo ""
    print_info "Deploying stripe-webhook function..."
    supabase functions deploy stripe-webhook
    
    if [ $? -eq 0 ]; then
        print_status "stripe-webhook deployed successfully"
    else
        print_error "Failed to deploy stripe-webhook"
        exit 1
    fi
    
    # Deploy stripe-portal
    echo ""
    print_info "Deploying stripe-portal function..."
    supabase functions deploy stripe-portal
    
    if [ $? -eq 0 ]; then
        print_status "stripe-portal deployed successfully"
    else
        print_error "Failed to deploy stripe-portal"
        exit 1
    fi
}

# Show deployment info
show_deployment_info() {
    echo ""
    print_status "🎉 All Edge Functions deployed successfully!"
    echo ""
    print_info "📋 Function URLs:"
    echo "   Checkout: $SUPABASE_URL/functions/v1/stripe-checkout"
    echo "   Webhook:  $SUPABASE_URL/functions/v1/stripe-webhook"
    echo "   Portal:   $SUPABASE_URL/functions/v1/stripe-portal"
    echo ""
    print_info "📊 To view function logs:"
    echo "   supabase functions logs stripe-checkout"
    echo "   supabase functions logs stripe-webhook"
    echo "   supabase functions logs stripe-portal"
    echo ""
    print_info "🔧 Next steps:"
    echo "   1. Update your .env file:"
    echo "      VITE_EDGE_FUNCTIONS_URL=$SUPABASE_URL/functions/v1"
    echo "   2. Test the integration in your application"
    echo "   3. Monitor function logs for any issues"
    echo ""
    print_info "🎯 Stripe Webhook URL (already configured):"
    echo "   $SUPABASE_URL/functions/v1/stripe-webhook"
}

# Main execution
main() {
    check_supabase_cli
    login_supabase
    setup_project
    set_secrets
    deploy_functions
    show_deployment_info
}

# Run main function
main
