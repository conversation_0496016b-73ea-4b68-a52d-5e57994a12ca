const Stripe = require('stripe');

// Initialize Stripe with your secret key
const stripe = new Stripe('sk_test_51RIcDWQXmlk54RXco22WsOUYY2jtE27RhFyvbaaiInFpIDD5yFcbQNPjSilbv6pzKxq1YuihBgAy4ria1OV3l2mY00gw9ED5fy');

// Webhook URL para suas Edge Functions
const WEBHOOK_URL = 'https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-webhook';

// Eventos essenciais para funcionalidade completa
const ESSENTIAL_EVENTS = [
  // Checkout Events
  'checkout.session.completed',
  'checkout.session.expired',
  
  // Subscription Events  
  'customer.subscription.created',
  'customer.subscription.updated',
  'customer.subscription.deleted',
  'customer.subscription.paused',
  'customer.subscription.resumed',
  'customer.subscription.trial_will_end',
  
  // Invoice Events
  'invoice.payment_succeeded',
  'invoice.payment_failed',
  'invoice.created',
  'invoice.finalized',
  'invoice.payment_action_required',
  'invoice.upcoming',
  
  // Customer Events
  'customer.created',
  'customer.updated',
  'customer.deleted',
  
  // Payment Events
  'payment_intent.succeeded',
  'payment_intent.payment_failed',
  'payment_intent.canceled',
  
  // Charge Events
  'charge.succeeded',
  'charge.failed',
  'charge.refunded',
  'charge.dispute.created'
];

async function createWebhook() {
  console.log('🎯 CRIANDO WEBHOOK NO STRIPE');
  console.log('============================\n');

  try {
    // 1. Verificar webhooks existentes
    console.log('1. 🔍 Verificando webhooks existentes...');
    const existingWebhooks = await stripe.webhookEndpoints.list();
    
    console.log(`   Encontrados ${existingWebhooks.data.length} webhooks existentes`);
    
    // Verificar se já existe um webhook para nossa URL
    const existingWebhook = existingWebhooks.data.find(
      webhook => webhook.url === WEBHOOK_URL
    );

    if (existingWebhook) {
      console.log(`⚠️  Webhook já existe: ${existingWebhook.id}`);
      console.log('🗑️  Removendo webhook existente...');
      
      try {
        await stripe.webhookEndpoints.del(existingWebhook.id);
        console.log('✅ Webhook existente removido');
      } catch (deleteError) {
        console.log('⚠️  Erro ao remover webhook existente:', deleteError.message);
      }
    }

    // 2. Criar novo webhook
    console.log('\n2. 🚀 Criando novo webhook...');
    console.log(`   URL: ${WEBHOOK_URL}`);
    console.log(`   Eventos: ${ESSENTIAL_EVENTS.length} eventos configurados`);

    const webhook = await stripe.webhookEndpoints.create({
      url: WEBHOOK_URL,
      enabled_events: ESSENTIAL_EVENTS,
      description: 'Paretto Book Platform - Production Webhook',
      metadata: {
        environment: 'production',
        platform: 'paretto',
        version: '1.0',
        created_by: 'automated_setup',
        project_id: 'qmeelujsnpbcdkzhcwmm'
      }
    });

    console.log('\n✅ WEBHOOK CRIADO COM SUCESSO!');
    console.log('==============================');
    console.log(`📋 ID: ${webhook.id}`);
    console.log(`🌐 URL: ${webhook.url}`);
    console.log(`📊 Status: ${webhook.status}`);
    console.log(`🎯 Eventos: ${webhook.enabled_events.length} configurados`);

    console.log('\n🔑 WEBHOOK SECRET (IMPORTANTE):');
    console.log('===============================');
    console.log(`${webhook.secret}`);
    console.log('===============================');

    console.log('\n📝 PRÓXIMOS PASSOS:');
    console.log('===================');
    console.log('1. ✅ Webhook criado no Stripe');
    console.log('2. 🔐 Configure o secret no Supabase:');
    console.log(`   npx supabase secrets set STRIPE_WEBHOOK_SECRET="${webhook.secret}"`);
    console.log('3. 🚀 Deploy suas Edge Functions');
    console.log('4. 🧪 Teste a integração');

    console.log('\n🎯 EVENTOS CONFIGURADOS:');
    console.log('========================');
    ESSENTIAL_EVENTS.forEach((event, index) => {
      console.log(`${(index + 1).toString().padStart(2, ' ')}. ${event}`);
    });

    console.log('\n🎉 CONFIGURAÇÃO COMPLETA!');
    console.log('Seu webhook está pronto para receber eventos do Stripe.');

    return {
      webhookId: webhook.id,
      webhookSecret: webhook.secret,
      webhookUrl: webhook.url
    };

  } catch (error) {
    console.error('\n❌ ERRO AO CRIAR WEBHOOK:', error.message);
    
    if (error.type === 'StripeAuthenticationError') {
      console.log('\n💡 Verifique se sua Stripe Secret Key está correta.');
    } else if (error.type === 'StripeInvalidRequestError') {
      console.log('\n💡 Verifique se a URL do webhook está acessível.');
      console.log('   URL testada:', WEBHOOK_URL);
    }
    
    throw error;
  }
}

// Função para testar o webhook
async function testWebhook(webhookId) {
  console.log('\n🧪 TESTANDO WEBHOOK...');
  console.log('======================');
  
  try {
    // Recuperar o webhook criado
    const webhook = await stripe.webhookEndpoints.retrieve(webhookId);
    
    console.log(`✅ Webhook encontrado: ${webhook.id}`);
    console.log(`📊 Status: ${webhook.status}`);
    console.log(`🎯 Eventos ativos: ${webhook.enabled_events.length}`);
    
    return true;
  } catch (error) {
    console.error('❌ Erro ao testar webhook:', error.message);
    return false;
  }
}

// Executar criação do webhook
async function main() {
  try {
    const result = await createWebhook();
    
    // Testar o webhook criado
    await testWebhook(result.webhookId);
    
    console.log('\n🎯 RESUMO FINAL:');
    console.log('================');
    console.log(`Webhook ID: ${result.webhookId}`);
    console.log(`Webhook URL: ${result.webhookUrl}`);
    console.log(`Webhook Secret: ${result.webhookSecret}`);
    console.log('');
    console.log('🚀 Próximo passo: Configure o secret no Supabase e faça deploy das Edge Functions!');
    
  } catch (error) {
    console.error('❌ Falha na configuração:', error.message);
    process.exit(1);
  }
}

// Executar
main();
