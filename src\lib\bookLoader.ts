import { supabase } from './supabase';

interface BookContent {
  id: string;
  title: string;
  author: string;
  category: string;
  pages: number;
  duration: number;
  difficulty: string;
  description: string;
  cover_image_url?: string;
  content: {
    chapters: Array<{
      id: string;
      title: string;
      content: string;
    }>;
    key_points: string[];
    practical_exercises?: string[];
  };
}

export class BookLoader {
  private static cache = new Map<string, BookContent>();

  // Method to clear cache - useful after database changes
  static clearCache(): void {
    this.cache.clear();

    // Also clear browser storage caches
    try {
      localStorage.removeItem('books_cache');
      localStorage.removeItem('book_content_cache');
      sessionStorage.removeItem('books_cache');
      sessionStorage.removeItem('book_content_cache');
      console.log('BookLoader: All caches cleared (memory + browser storage)');
    } catch (error) {
      console.log('BookLoader: Memory cache cleared, browser storage may not be available');
    }
  }

  // Method to remove specific book from cache
  static removeCachedBook(bookId: string): void {
    this.cache.delete(bookId);
    console.log('Removed book from cache:', bookId);
  }

  static async loadBook(bookId: string): Promise<BookContent | null> {
    console.log('Loading book with ID:', bookId);

    // Check cache first
    if (this.cache.has(bookId)) {
      console.log('Book found in cache');
      return this.cache.get(bookId)!;
    }

    try {
      // First, get book basic info from books table
      const { data: bookData, error: bookError } = await supabase
        .from('books')
        .select('*')
        .eq('id', bookId)
        .single();

      if (bookError || !bookData) {
        console.error('Error loading book data:', bookError);
        return this.createFallbackContent(bookId);
      }

      console.log('Book data loaded:', bookData.title);

      // Try to get book content from book_contents table
      const { data: contentData, error: contentError } = await supabase
        .from('book_contents')
        .select('content')
        .eq('book_id', bookId)
        .single();

      let bookContent: BookContent;

      if (contentError || !contentData) {
        console.warn('No content found in database for book ID:', bookId, 'Error:', contentError);
        // Create content based on book data
        bookContent = this.createContentFromBookData(bookData);
      } else {
        console.log('Book content loaded from database');
        // Combine book data with content
        bookContent = {
          id: String(bookData.id),
          title: bookData.title,
          author: bookData.author || 'Autor Desconhecido',
          category: bookData.category || 'Geral',
          pages: bookData.pages || 300,
          duration: bookData.duration || 20,
          difficulty: bookData.difficulty || 'Intermediário',
          description: bookData.description || 'Descrição não disponível',
          cover_image_url: bookData.cover_image_url,
          content: contentData.content
        };
      }

      // Cache the loaded content
      this.cache.set(bookId, bookContent);

      return bookContent;
    } catch (error) {
      console.error('Error loading book content:', error);
      return this.createFallbackContent(bookId);
    }
  }

  private static createFallbackContent(bookId: string): BookContent {
    // Create fallback content based on book ID
    const fallbackBooks: Record<string, Partial<BookContent>> = {
      '1': {
        title: 'Hábitos Atômicos',
        author: 'James Clear',
        category: 'Desenvolvimento Pessoal',
        description: 'Um guia fácil e comprovado para criar bons hábitos e se livrar dos maus'
      },
      '2': {
        title: 'O Investidor Inteligente',
        author: 'Benjamin Graham',
        category: 'Finanças',
        description: 'O livro definitivo sobre investimento em valor'
      }
    };

    const fallback = fallbackBooks[String(bookId)] || {
      title: 'Livro Não Encontrado',
      author: 'Autor Desconhecido',
      category: 'Geral',
      description: 'Conteúdo não disponível'
    };

    return {
      id: String(bookId),
      title: fallback.title!,
      author: fallback.author!,
      category: fallback.category!,
      pages: 300,
      duration: 20,
      difficulty: 'Intermediário',
      description: fallback.description!,
      content: {
        chapters: [
          {
            id: 'chapter1',
            title: 'Introdução',
            content: this.generateSampleContent(fallback.title!)
          }
        ],
        key_points: [
          'Este é um resumo de exemplo',
          'O conteúdo completo estará disponível em breve',
          'Continue explorando nossa biblioteca'
        ]
      }
    };
  }

  private static createContentFromBookData(bookData: any): BookContent {
    console.log('Creating content from book data for:', bookData.title);

    // Create basic content structure when no content is found in database
    const content = {
      chapters: [
        {
          id: 'chapter1',
          title: 'Conteúdo Principal',
          content: `Este livro apresenta insights valiosos sobre ${bookData.category?.toLowerCase() || 'o tema abordado'}.

O conteúdo completo está sendo processado e estará disponível em breve. Nossa equipe está trabalhando para fornecer o texto integral extraído do PDF original.

Principais características desta obra:
• Abordagem prática e aplicável
• Conceitos fundamentais bem estruturados
• Exemplos relevantes e atuais
• Metodologia comprovada

Continue explorando nossa biblioteca para descobrir outros conteúdos já disponíveis com texto completo.`
        }
      ],
      key_points: [
        'Conteúdo em processamento - texto completo em breve',
        'Obra relevante na área de ' + (bookData.category || 'conhecimento geral'),
        'Metodologia estruturada para máximo aprendizado',
        'Aplicação prática dos conceitos apresentados'
      ],
      practical_exercises: [
        'Aguarde o processamento completo do conteúdo',
        'Explore outros livros já disponíveis na biblioteca',
        'Marque este livro como favorito para acompanhar atualizações'
      ]
    };

    return {
      id: String(bookData.id),
      title: bookData.title,
      author: bookData.author || 'Autor Desconhecido',
      category: bookData.category || 'Geral',
      pages: bookData.pages || 300,
      duration: bookData.duration || 20,
      difficulty: bookData.difficulty || 'Intermediário',
      description: bookData.description || 'Descrição não disponível',
      cover_image_url: bookData.cover_image_url,
      content: content
    };
  }

  private static generateSampleContent(title: string): string {
    return `# ${title}

Este é um resumo profissional que captura os insights mais importantes e aplicáveis do livro original. Nossa metodologia garante que você absorva 95% do conhecimento essencial em apenas 20% do tempo de leitura.

## Principais Conceitos

O livro apresenta conceitos fundamentais que podem transformar sua perspectiva e abordagem em relação ao tema central. Através de exemplos práticos e estratégias comprovadas, o autor oferece um guia completo para implementação.

## Estratégias Práticas

**Implementação Gradual**

A chave para o sucesso está na implementação gradual e consistente dos conceitos apresentados. Comece com pequenas mudanças e construa momentum ao longo do tempo.

**Aplicação no Dia a Dia**

- Identifique oportunidades de aplicação imediata
- Desenvolva um sistema de acompanhamento
- Mantenha consistência nas práticas
- Ajuste a abordagem conforme necessário

## Resultados Esperados

Com a aplicação consistente dos princípios apresentados, você pode esperar:

- Melhoria significativa na área de foco
- Desenvolvimento de novas habilidades
- Maior clareza sobre objetivos e prioridades
- Resultados mensuráveis a médio e longo prazo

## Conclusão

Este resumo oferece uma base sólida para compreensão e aplicação dos conceitos principais. Continue sua jornada de aprendizado explorando outros resumos em nossa biblioteca.

**Próximos Passos:**
1. Reflita sobre os conceitos apresentados
2. Identifique áreas de aplicação imediata
3. Desenvolva um plano de implementação
4. Monitore o progresso regularmente
5. Ajuste a estratégia conforme necessário`;
  }

  static async getBookText(bookId: string): Promise<string> {
    console.log('Getting book text for ID:', bookId);

    // First check if this book has a PDF file in the pdf_files table
    const { data: pdfData, error: pdfError } = await supabase
      .from('pdf_files')
      .select('id, filename')
      .eq('book_id', bookId)
      .single();

    if (!pdfError && pdfData) {
      console.log('Book has PDF file in database:', pdfData.filename);
      // Return a special marker to indicate this should use the PDF viewer
      return '<PDF_VIEWER_PLACEHOLDER>';
    }

    // Fallback: check if book has pdf_key (legacy method)
    const { data: bookData, error: bookError } = await supabase
      .from('books')
      .select('pdf_key')
      .eq('id', bookId)
      .single();

    if (!bookError && bookData?.pdf_key) {
      console.log('Book has PDF key:', bookData.pdf_key);
      // Return a special marker to indicate this should use the PDF viewer
      return '<PDF_VIEWER_PLACEHOLDER>';
    }

    const book = await this.loadBook(bookId);
    if (!book) {
      return 'Conteúdo não encontrado. Verifique se o arquivo do livro existe.';
    }

    console.log('Converting book to text:', book.title);

    // Check if content exists
    if (!book.content || !book.content.chapters || book.content.chapters.length === 0) {
      // No structured content, create basic structure
      let text = `# ${book.title}\n\n`;

      if (book.author && book.author !== 'Autor Desconhecido') {
        text += `**Por ${book.author}**\n\n`;
      }

      if (book.description && book.description.length > 50 &&
          !book.description.includes('Resumo completo do livro')) {
        text += `${book.description}\n\n`;
      }

      text += 'Conteúdo não disponível no momento.\n\n';
      return text;
    }

    // Check if this is an embedded PDF
    if (book.content.pdf_data && book.content.storage_method === 'base64_embedded') {
      console.log('Detected embedded PDF content');
      return this.handleEmbeddedPDF(book);
    }

    // Check if the first chapter already contains a complete book structure
    const firstChapter = book.content.chapters[0];
    const hasCompleteStructure = firstChapter && firstChapter.content && (
      firstChapter.content.includes(`# ${book.title}`) ||
      firstChapter.content.includes('## INTRODUÇÃO') ||
      firstChapter.content.includes('## Introdução')
    );

    let text = '';

    if (hasCompleteStructure) {
      // Content already has complete structure, use it directly
      console.log('Using complete structured content');
      book.content.chapters.forEach((chapter, index) => {
        if (chapter.content && chapter.content.trim().length > 0) {
          const structuredContent = this.parseChapterContent(chapter.content, chapter.title, index + 1);
          text += structuredContent;
        }
      });
    } else {
      // Content needs header information
      console.log('Adding header to content');
      text = `# ${book.title}\n\n`;

      if (book.author && book.author !== 'Autor Desconhecido') {
        text += `**Por ${book.author}**\n\n`;
      }

      if (book.description && book.description.length > 50 &&
          !book.description.includes('Resumo completo do livro')) {
        text += `${book.description}\n\n`;
      }

      // Process chapters
      book.content.chapters.forEach((chapter, index) => {
        if (chapter.content && chapter.content.trim().length > 0) {
          const structuredContent = this.parseChapterContent(chapter.content, chapter.title, index + 1);
          text += structuredContent;
        }
      });
    }
    
    // Add key points
    if (book.content.key_points && book.content.key_points.length > 0) {
      text += `## Pontos-Chave\n\n`;
      book.content.key_points.forEach(point => {
        text += `- ${point}\n`;
      });
      text += '\n';
    }
    
    // Add practical exercises if available
    if (book.content.practical_exercises && book.content.practical_exercises.length > 0) {
      text += `## Exercícios Práticos\n\n`;
      book.content.practical_exercises.forEach((exercise, index) => {
        text += `${index + 1}. ${exercise}\n`;
      });
    }
    
    console.log('Text generated, length:', text.length);
    return text;
  }

  static formatTextForReader(text: string, theme: 'light' | 'dark' | 'sepia' = 'light'): string {
    if (!text || text.trim() === '') {
      return '<p class="text-center text-gray-500">Conteúdo não disponível</p>';
    }

    // Clean up any unwanted markers or artifacts (but preserve markdown headers)
    text = text
      .replace(/\$\d+/g, '') // Remove $1, $2, $3, etc.
      .replace(/\$[a-zA-Z]+/g, '') // Remove $variable patterns
      .replace(/[%¨&*]/g, '') // Remove caracteres especiais indesejados (but keep # for headers)
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    // Colors are now handled by CSS classes, no need for theme-specific colors here

    // Aplicar formatação profissional seguindo as especificações
    console.log('Formatting text:', text.substring(0, 100));

    // Normalizar quebras de linha e dividir o texto
    const normalizedText = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    let lines = normalizedText.split('\n');

    // If we have very few lines but long content, try to split on common patterns
    if (lines.length < 10 && text.length > 1000) {
      console.log('Detected merged text, attempting to split on patterns...');
      // Split on markdown headers and common patterns
      const splitText = text
        .replace(/(\n|^)(#{1,3}\s)/g, '\n$2') // Ensure headers are on new lines
        .replace(/(\*\*[^*]+\*\*)/g, '\n$1\n') // Split on bold text
        .replace(/([.!?])\s+([A-Z])/g, '$1\n\n$2') // Split sentences into paragraphs
        .replace(/\n\s*\n\s*\n/g, '\n\n'); // Clean up multiple newlines

      lines = splitText.split('\n');
      console.log('After pattern splitting:', lines.length, 'lines');
    }

    const processedLines = [];
    console.log('Total lines to process:', lines.length);

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (line === '') {
        processedLines.push('');
        continue;
      }

      // 1. Títulos de capítulo (# Título ou palavras como "CAPÍTULO")
      if (line.startsWith('# ') || /^(CAPÍTULO|CHAPTER|CAP\.?)\s+/i.test(line)) {
        let title = line.startsWith('# ') ? line.substring(2) : line;
        title = title.toUpperCase().trim();
        console.log('Found chapter title:', title);
        processedLines.push(`<div class="chapter-title">${title}</div>`);
        continue;
      }

      // 2. Subtítulos (## Título ou seções) - More aggressive detection
      if (line.startsWith('## ') ||
          /^(SEÇÃO|SECTION|PARTE|PART|INTRODUÇÃO|INTRODUCTION)\s+/i.test(line) ||
          /^[A-Z\s]{5,50}$/.test(line.trim()) && line.trim().length > 10) {
        let title = line.startsWith('## ') ? line.substring(3) : line;
        title = title.toUpperCase().trim();
        console.log('Found section title:', title);
        processedLines.push(`<div class="section-title">${title}</div>`);
        continue;
      }

      // 3. Sub-subtítulos (### Título)
      if (line.startsWith('### ')) {
        const title = line.substring(4).toUpperCase().trim();
        console.log('Found subsection title:', title);
        processedLines.push(`<div class="subsection-title">${title}</div>`);
        continue;
      }

      // 3. Detectar listas numeradas (visualmente distintas do texto principal)
      if (/^\d+\.\s/.test(line)) {
        const listItem = line.replace(/^(\d+)\.\s(.+)$/, '$1. $2');
        processedLines.push(`<div class="numbered-list">${listItem}</div>`);
        continue;
      }

      // 4. Detectar listas com marcadores
      if (line.startsWith('- ') || line.startsWith('• ')) {
        const listItem = line.substring(2).trim();
        processedLines.push(`<div class="numbered-list">• ${listItem}</div>`);
        continue;
      }

      // 5. Detectar texto enfatizado (entre asteriscos)
      if (line.startsWith('**') && line.endsWith('**') && line.length > 4) {
        const emphasized = line.substring(2, line.length - 2);
        processedLines.push(`<div class="emphasized-text">${emphasized}</div>`);
        continue;
      }

      // 6. Remover formatação markdown restante
      let cleanLine = line
        .replace(/^\*\*(.+)\*\*$/, '$1')
        .replace(/\*\*(.+?)\*\*/g, '$1');

      // 7. Corpo do texto principal
      processedLines.push(`<p class="body-text">${cleanLine}</p>`);
    }

    return processedLines.join('\n');
  }

  // Parse chapter content to extract proper structure and eliminate duplication
  private static parseChapterContent(content: string, chapterTitle: string, chapterNumber: number): string {
    console.log(`Parsing chapter ${chapterNumber}: ${chapterTitle}`);

    // Clean and normalize the content
    let cleanContent = content
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .trim();

    // Remove obvious duplicates by splitting into paragraphs and deduplicating
    const paragraphs = cleanContent.split(/\n\s*\n/);
    const uniqueParagraphs = [];
    const seenParagraphs = new Set();

    for (const paragraph of paragraphs) {
      const trimmed = paragraph.trim();
      if (trimmed.length > 20) { // Only consider substantial paragraphs
        const normalized = trimmed.toLowerCase().replace(/\s+/g, ' ');
        if (!seenParagraphs.has(normalized)) {
          seenParagraphs.add(normalized);
          uniqueParagraphs.push(trimmed);
        }
      }
    }

    console.log(`Deduplicated: ${paragraphs.length} -> ${uniqueParagraphs.length} paragraphs`);

    // Try to identify structure within the content
    let structuredText = '';

    // Add chapter heading if the title is meaningful
    if (chapterTitle && chapterTitle !== 'Seção 1' && chapterTitle !== 'Chapter 1') {
      structuredText += `## ${chapterTitle.toUpperCase()}\n\n`;
    }

    // Process each paragraph to identify potential subheadings
    for (let i = 0; i < uniqueParagraphs.length; i++) {
      const paragraph = uniqueParagraphs[i];

      // Check if this paragraph looks like a subheading
      if (this.isLikelySubheading(paragraph)) {
        structuredText += `### ${paragraph}\n\n`;
      }
      // Check if it's a numbered list item
      else if (/^\d+\.\s/.test(paragraph)) {
        structuredText += `${paragraph}\n\n`;
      }
      // Check if it's a bullet point
      else if (/^[-•]\s/.test(paragraph)) {
        structuredText += `${paragraph}\n\n`;
      }
      // Regular paragraph
      else {
        structuredText += `${paragraph}\n\n`;
      }
    }

    return structuredText;
  }

  // Helper method to identify potential subheadings
  private static isLikelySubheading(text: string): boolean {
    // Characteristics of subheadings:
    // - Short (usually less than 100 characters)
    // - No punctuation at the end
    // - May contain keywords like "Introdução", "Conclusão", etc.
    // - All caps or title case
    // - Not starting with common paragraph words

    const trimmed = text.trim();

    if (trimmed.length > 100) return false;
    if (trimmed.endsWith('.') || trimmed.endsWith(',')) return false;

    // Common subheading patterns
    const subheadingPatterns = [
      /^(INTRODUÇÃO|INTRODUCTION|CONCLUSÃO|CONCLUSION)/i,
      /^(CAPÍTULO|CHAPTER|PARTE|PART|SEÇÃO|SECTION)/i,
      /^(RESUMO|SUMMARY|OVERVIEW|VISÃO GERAL)/i,
      /^[A-Z][A-Z\s]{5,50}$/,  // All caps text
      /^[A-Z][a-z]+(\s[A-Z][a-z]+)*:?$/  // Title case
    ];

    return subheadingPatterns.some(pattern => pattern.test(trimmed));
  }

  // Handle embedded PDF content
  private static handleEmbeddedPDF(book: any): string {
    // Return only the PDF viewer placeholder - no explanatory text
    return `<PDF_VIEWER_PLACEHOLDER>`;
  }
}

export type { BookContent };