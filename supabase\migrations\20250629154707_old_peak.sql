/*
  # Criar usuário administrador

  1. Criar usuário na tabela auth.users
  2. Criar entrada correspondente na tabela users
  3. Configurar políticas administrativas
  4. Criar função helper para verificar admin
*/

-- <PERSON><PERSON>, vamos criar o usuário na tabela auth.users
DO $$
DECLARE
  admin_user_id uuid := 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'::uuid;
  admin_email text := '<EMAIL>';
  admin_password text := 'Admin123!';
BEGIN
  -- Verificar se já existe na auth.users
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = admin_user_id OR email = admin_email) THEN
    -- Inserir na tabela auth.users
    INSERT INTO auth.users (
      id,
      instance_id,
      email,
      encrypted_password,
      email_confirmed_at,
      created_at,
      updated_at,
      role,
      aud,
      confirmation_token,
      email_change_token_new,
      recovery_token
    ) VALUES (
      admin_user_id,
      '00000000-0000-0000-0000-000000000000',
      admin_email,
      crypt(admin_password, gen_salt('bf')),
      now(),
      now(),
      now(),
      'authenticated',
      'authenticated',
      '',
      '',
      ''
    );
    
    RAISE NOTICE 'Usuário admin criado na auth.users com ID: %', admin_user_id;
  ELSE
    RAISE NOTICE 'Usuário admin já existe na auth.users';
  END IF;
  
  -- Verificar se já existe na tabela users
  IF NOT EXISTS (SELECT 1 FROM users WHERE id = admin_user_id OR email = admin_email) THEN
    -- Inserir na tabela users
    INSERT INTO users (
      id,
      email,
      name,
      role,
      subscription_plan,
      free_slots_available,
      is_free_trial,
      trial_end_date,
      last_monthly_slot,
      created_at,
      updated_at
    ) VALUES (
      admin_user_id,
      admin_email,
      'Administrador',
      'ADMIN',
      'PREMIUM',
      999999,
      false,
      now() + interval '10 years',
      now(),
      now(),
      now()
    );
    
    RAISE NOTICE 'Usuário admin criado na tabela users com ID: %', admin_user_id;
  ELSE
    RAISE NOTICE 'Usuário admin já existe na tabela users';
  END IF;
END $$;

-- Remover políticas existentes se existirem
DROP POLICY IF EXISTS "Admins can view all users" ON users;
DROP POLICY IF EXISTS "Admins can update any user" ON users;
DROP POLICY IF EXISTS "Admins can manage books" ON books;

-- Criar política para permitir que admins vejam todos os usuários
CREATE POLICY "Admins can view all users"
  ON users FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Criar política para permitir que admins atualizem qualquer usuário
CREATE POLICY "Admins can update any user"
  ON users FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Adicionar políticas para que admins possam gerenciar livros
CREATE POLICY "Admins can manage books"
  ON books FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Função para verificar se usuário é admin
CREATE OR REPLACE FUNCTION is_admin(user_id uuid DEFAULT auth.uid())
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = user_id AND role = 'ADMIN'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Atualizar a função de criação de usuário para não interferir com admins
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Só criar entrada na tabela users se não for o admin
  IF NEW.id != 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'::uuid THEN
    INSERT INTO users (
      id,
      email,
      name,
      role,
      subscription_plan,
      free_slots_available,
      is_free_trial,
      trial_end_date,
      last_monthly_slot,
      created_at,
      updated_at
    ) VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
      'USER',
      'FREE',
      5,
      true,
      now() + interval '30 days',
      now(),
      now(),
      now()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recriar o trigger se necessário
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();