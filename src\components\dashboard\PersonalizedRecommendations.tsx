import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Icons } from '@/components/ui/icons';
import { Button } from '@/components/ui/button';
import { dataService } from '@/lib/dataService';

interface Book {
  id: string;
  title: string;
  author: string;
  category: string;
  duration: number;
  difficulty: string;
  is_featured: boolean;
  description: string;
  cover_image_url?: string;
}

interface Recommendation {
  id: string;
  book_id: number;
  recommendation_score: number;
  recommendation_reason: string;
  book: Book;
}

interface PersonalizedRecommendationsProps {
  recommendations?: Recommendation[];
  fallbackBooks?: Book[];
  onReadBook: (bookId: string) => void;
}

export function PersonalizedRecommendations({ recommendations, fallbackBooks, onReadBook }: PersonalizedRecommendationsProps) {
  const [displayBooks, setDisplayBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (recommendations && recommendations.length > 0) {
      // Use personalized recommendations
      const books = recommendations.slice(0, 3).map(rec => rec.book);
      setDisplayBooks(books);
      setLoading(false);
    } else if (fallbackBooks && fallbackBooks.length > 0) {
      // Use fallback books (featured books)
      setDisplayBooks(fallbackBooks.slice(0, 3));
      setLoading(false);
    } else {
      // Generate recommendations if none exist
      generateRecommendations();
    }
  }, [recommendations, fallbackBooks]);

  const generateRecommendations = async () => {
    try {
      setLoading(true);
      const user = await dataService.getCurrentUser();
      if (user) {
        await dataService.generateRecommendations(user.id);
        // Reload recommendations
        const dashboardData = await dataService.getDashboardData();
        if (dashboardData.recommendations.length > 0) {
          const books = dashboardData.recommendations.slice(0, 3).map(rec => rec.book);
          setDisplayBooks(books);
        }
      }
    } catch (error) {
      console.error('Error generating recommendations:', error);
    } finally {
      setLoading(false);
    }
  };
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="bg-white rounded-2xl border border-gray-100 p-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-bold text-gray-900">Recomendado para Você</h3>
        <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
          <Icons.Star className="w-5 h-5 text-purple-500" />
        </div>
      </div>

      {/* Recommendations */}
      <div className="space-y-4">
        {loading ? (
          // Loading skeleton
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex items-start space-x-4 p-4 rounded-xl animate-pulse">
              <div className="w-12 h-16 bg-gray-200 rounded-lg flex-shrink-0"></div>
              <div className="flex-1 min-w-0">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          ))
        ) : displayBooks.length === 0 ? (
          <div className="text-center py-8">
            <Icons.Star className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500 text-sm">
              Continue lendo para receber recomendações personalizadas
            </p>
          </div>
        ) : (
          displayBooks.map((book, index) => (
          <motion.div
            key={book.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="group cursor-pointer"
            onClick={() => onReadBook(book.id)}
          >
            <div className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-50 transition-all duration-300">
              {/* Book Cover */}
              <div className="w-12 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-105 transition-transform duration-300">
                {book.cover_image_url ? (
                  <img 
                    src={book.cover_image_url} 
                    alt={book.title}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <Icons.BookOpen className="w-6 h-6 text-gray-400" />
                )}
              </div>

              {/* Book Info */}
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-gray-900 text-sm mb-1 line-clamp-2 group-hover:text-gray-800 transition-colors">
                  {book.title}
                </h4>
                <p className="text-xs text-gray-500 mb-2">
                  {book.author}
                </p>
                
                <div className="flex items-center space-x-3 text-xs text-gray-400">
                  <div className="flex items-center space-x-1">
                    <Icons.Clock className="w-3 h-3" />
                    <span>{book.duration} min</span>
                  </div>
                  <span className="w-1 h-1 bg-gray-300 rounded-full" />
                  <span className="capitalize">{book.category}</span>
                </div>
              </div>

              {/* Arrow */}
              <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icons.ArrowRight className="w-4 h-4 text-gray-400" />
              </div>
            </div>
          </motion.div>
          ))
        )}
      </div>

      {/* CTA */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <Button 
          variant="ghost" 
          className="w-full text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50"
        >
          Ver mais recomendações
        </Button>
      </div>

      {/* Personalization Note */}
      <div className="mt-4 p-3 bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl">
        <p className="text-xs text-gray-600 text-center">
          <span className="font-medium">💡 Dica:</span> Baseado no seu histórico de leitura e preferências
        </p>
      </div>
    </motion.div>
  );
}