-- Verification script to check book removal status
-- Execute this AFTER running remove-books.sql

-- Check if the target books still exist
SELECT 'VERIFICATION: Target books status' as check_name;

SELECT 
    'Books matching "Pense e Enriqueça" criteria' as book_type,
    COUNT(*) as count,
    STRING_AGG(CONCAT(id, ': ', title, ' by ', author), '; ') as books_found
FROM books 
WHERE LOWER(title) LIKE '%pense e enriqueça%' 
   OR LOWER(title) LIKE '%pense e enriqueca%'
   OR (LOWER(title) LIKE '%pense%' AND LOWER(author) LIKE '%napoleon hill%')

UNION ALL

SELECT 
    'Books matching "Gestalt-Terapia" criteria' as book_type,
    COUNT(*) as count,
    STRING_AGG(CONCAT(id, ': ', title, ' by ', author), '; ') as books_found
FROM books 
WHERE LOWER(title) LIKE '%gestalt%terapia%' 
   OR LOWER(title) LIKE '%gestalt-terapia%'
   OR (LOWER(title) LIKE '%gestalt%' AND <PERSON><PERSON><PERSON>(author) LIKE '%perls%');

-- Check for any remaining Napoleon Hill books
SELECT 'VERIFICATION: Napoleon Hill books remaining' as check_name;
SELECT id, title, author, category, duration, is_featured
FROM books 
WHERE LOWER(author) LIKE '%napoleon hill%'
ORDER BY id;

-- Check for any remaining Gestalt books
SELECT 'VERIFICATION: Gestalt-related books remaining' as check_name;
SELECT id, title, author, category, duration, is_featured
FROM books 
WHERE LOWER(title) LIKE '%gestalt%'
ORDER BY id;

-- Check user progress data for removed books
SELECT 'VERIFICATION: User progress for removed books' as check_name;
SELECT COUNT(*) as orphaned_progress_records
FROM user_reading_progress urp
WHERE NOT EXISTS (
    SELECT 1 FROM books b WHERE b.id::TEXT = urp.book_id
);

-- Check summaries data for removed books
SELECT 'VERIFICATION: Summaries for removed books' as check_name;
SELECT COUNT(*) as orphaned_summary_records
FROM summaries s
WHERE NOT EXISTS (
    SELECT 1 FROM books b WHERE b.id = s.book_id
);

-- Check book contents for removed books
SELECT 'VERIFICATION: Book contents for removed books' as check_name;
SELECT COUNT(*) as orphaned_content_records
FROM book_contents bc
WHERE NOT EXISTS (
    SELECT 1 FROM books b WHERE b.id = bc.book_id
);

-- Show library statistics after removal
SELECT 'LIBRARY STATISTICS AFTER REMOVAL' as check_name;

SELECT 
    'Total books' as metric,
    COUNT(*) as value
FROM books

UNION ALL

SELECT 
    'Featured books' as metric,
    COUNT(*) as value
FROM books 
WHERE is_featured = true

UNION ALL

SELECT 
    'Free books' as metric,
    COUNT(*) as value
FROM books 
WHERE is_free = true

UNION ALL

SELECT 
    'Books by category' as metric,
    COUNT(*) as value
FROM books
GROUP BY category;

-- Show sample of remaining books to ensure library integrity
SELECT 'SAMPLE OF REMAINING BOOKS' as check_name;
SELECT 
    id, 
    title, 
    author, 
    category, 
    duration,
    difficulty,
    is_featured,
    is_free
FROM books 
WHERE is_featured = true
ORDER BY id 
LIMIT 10;

-- Final confirmation message
SELECT 'REMOVAL VERIFICATION COMPLETE' as status,
       'If counts above are 0 for target books, removal was successful' as message;
