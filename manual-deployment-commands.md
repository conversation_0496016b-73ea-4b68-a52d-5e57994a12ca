# 🚀 MANUAL DEPLOYMENT COMMANDS

Se os scripts automáticos não funcionarem, execute estes comandos manualmente:

## 📋 **Pré-requisitos**

1. **Instalar Supabase CLI**:
```bash
npm install -g supabase
```

2. **Verificar instalação**:
```bash
supabase --version
```

## 🔐 **1. Login e Setup**

```bash
# Login no Supabase
supabase login

# Inicializar projeto (se necessário)
supabase init

# Linkar ao projeto
supabase link --project-ref qmeelujsnpbcdkzhcwmm
```

## 🔑 **2. Configurar Secrets**

```bash
# Stripe Secret Key
supabase secrets set STRIPE_SECRET_KEY="sk_test_51RIcDWQXmlk54RXco22WsOUYY2jtE27RhFyvbaaiInFpIDD5yFcbQNPjSilbv6pzKxq1YuihBgAy4ria1OV3l2mY00gw9ED5fy"

# Stripe Webhook Secret
supabase secrets set STRIPE_WEBHOOK_SECRET="whsec_rSOSlhBwwbkRSVfgqla96EctF9NTcL9c"

# Supabase URL
supabase secrets set SUPABASE_URL="https://qmeelujsnpbcdkzhcwmm.supabase.co"

# Supabase Anon Key
supabase secrets set SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4"

# Verificar secrets
supabase secrets list
```

## 🚀 **3. Deploy das Functions**

```bash
# Deploy stripe-checkout
supabase functions deploy stripe-checkout

# Deploy stripe-webhook
supabase functions deploy stripe-webhook

# Deploy stripe-portal
supabase functions deploy stripe-portal

# Listar functions deployadas
supabase functions list
```

## 📊 **4. Verificar Deployment**

```bash
# Ver logs das functions
supabase functions logs stripe-checkout
supabase functions logs stripe-webhook
supabase functions logs stripe-portal

# Status do projeto
supabase status
```

## 🔧 **5. URLs das Functions**

Após o deploy, suas functions estarão disponíveis em:

- **Checkout**: `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-checkout`
- **Webhook**: `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-webhook`
- **Portal**: `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal`

## 🧪 **6. Testar Functions**

### Testar Checkout:
```bash
curl -X POST https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-checkout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4" \
  -d '{
    "price_id": "price_1Rov58QXmlk54RXcFlW7ymIL",
    "user_id": "test-user-123",
    "success_url": "http://localhost:5173/subscription/success",
    "cancel_url": "http://localhost:5173/subscription/cancel",
    "plan_id": "premium"
  }'
```

### Testar Portal:
```bash
curl -X POST https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4" \
  -d '{
    "customer_id": "cus_test_customer_id",
    "return_url": "http://localhost:5173/subscription"
  }'
```

## ⚠️ **Troubleshooting**

### Erro de Autenticação:
```bash
# Re-login
supabase logout
supabase login
```

### Erro de Link:
```bash
# Re-link projeto
supabase unlink
supabase link --project-ref qmeelujsnpbcdkzhcwmm
```

### Erro de Deploy:
```bash
# Verificar status
supabase status

# Ver logs detalhados
supabase functions logs --follow stripe-checkout
```

### Erro de Secrets:
```bash
# Listar secrets atuais
supabase secrets list

# Remover secret problemático
supabase secrets unset STRIPE_SECRET_KEY

# Reconfigurar
supabase secrets set STRIPE_SECRET_KEY="sua_chave_aqui"
```

## 📝 **Após Deploy Bem-sucedido**

1. **Atualizar .env**:
```env
VITE_EDGE_FUNCTIONS_URL=https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1
VITE_USE_PRODUCTION_STRIPE=true
```

2. **Testar na aplicação**:
   - Navegar para seção "Assinatura"
   - Tentar upgrade para Premium
   - Verificar logs das functions

3. **Monitorar**:
   - Dashboard Supabase: https://supabase.com/dashboard/project/qmeelujsnpbcdkzhcwmm
   - Dashboard Stripe: https://dashboard.stripe.com/test/webhooks
   - Logs: `supabase functions logs stripe-webhook --follow`

## 🎯 **Webhook já Configurado**

O webhook já foi criado no Stripe com a URL:
`https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-webhook`

Todos os eventos necessários já estão configurados!
