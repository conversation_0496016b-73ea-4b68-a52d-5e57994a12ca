import { supabase } from './supabase'
import { RealtimeChannel } from '@supabase/supabase-js'

export interface RealtimeSubscription {
  channel: RealtimeChannel
  unsubscribe: () => void
}

export interface DashboardUpdate {
  type: 'favorites' | 'progress' | 'preferences' | 'subscription' | 'privacy' | 'statistics'
  data: any
  userId: string
  timestamp: string
}

class RealtimeManager {
  private subscriptions: Map<string, RealtimeSubscription> = new Map()
  private listeners: Map<string, Set<(update: DashboardUpdate) => void>> = new Map()

  /**
   * Subscribe to real-time updates for a specific data type
   */
  subscribe(
    type: DashboardUpdate['type'],
    userId: string,
    callback: (update: DashboardUpdate) => void
  ): () => void {
    console.log(`🔄 Setting up real-time subscription for ${type}:`, userId)

    const subscriptionKey = `${type}_${userId}`
    
    // Add callback to listeners
    if (!this.listeners.has(subscriptionKey)) {
      this.listeners.set(subscriptionKey, new Set())
    }
    this.listeners.get(subscriptionKey)!.add(callback)

    // Create Supabase subscription if not exists
    if (!this.subscriptions.has(subscriptionKey)) {
      this.createSupabaseSubscription(type, userId, subscriptionKey)
    }

    // Return unsubscribe function
    return () => {
      this.listeners.get(subscriptionKey)?.delete(callback)
      
      // If no more listeners, cleanup subscription
      if (this.listeners.get(subscriptionKey)?.size === 0) {
        this.cleanup(subscriptionKey)
      }
    }
  }

  private createSupabaseSubscription(
    type: DashboardUpdate['type'],
    userId: string,
    subscriptionKey: string
  ) {
    let tableName: string
    let filter: string

    switch (type) {
      case 'favorites':
      case 'progress':
        tableName = 'user_reading_progress'
        filter = `user_id=eq.${userId}`
        break
      case 'preferences':
        tableName = 'user_preferences'
        filter = `user_id=eq.${userId}`
        break
      case 'subscription':
        tableName = 'user_subscriptions'
        filter = `user_id=eq.${userId}`
        break
      case 'privacy':
        tableName = 'user_preferences'
        filter = `user_id=eq.${userId}`
        break
      case 'statistics':
        tableName = 'user_reading_streak'
        filter = `user_id=eq.${userId}`
        break
      default:
        console.error('❌ Unknown subscription type:', type)
        return
    }

    const channel = supabase
      .channel(`${subscriptionKey}_channel`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: tableName,
          filter: filter
        },
        (payload) => {
          console.log(`📡 Real-time update received for ${type}:`, payload)
          this.handleRealtimeUpdate(type, userId, payload)
        }
      )
      .subscribe((status) => {
        console.log(`📡 Subscription status for ${type}:`, status)
      })

    this.subscriptions.set(subscriptionKey, {
      channel,
      unsubscribe: () => {
        supabase.removeChannel(channel)
      }
    })
  }

  private handleRealtimeUpdate(
    type: DashboardUpdate['type'],
    userId: string,
    payload: any
  ) {
    const subscriptionKey = `${type}_${userId}`
    const listeners = this.listeners.get(subscriptionKey)

    if (listeners && listeners.size > 0) {
      const update: DashboardUpdate = {
        type,
        data: payload.new || payload.old,
        userId,
        timestamp: new Date().toISOString()
      }

      listeners.forEach(callback => {
        try {
          callback(update)
        } catch (error) {
          console.error(`❌ Error in real-time callback for ${type}:`, error)
        }
      })
    }
  }

  private cleanup(subscriptionKey: string) {
    const subscription = this.subscriptions.get(subscriptionKey)
    if (subscription) {
      subscription.unsubscribe()
      this.subscriptions.delete(subscriptionKey)
    }
    this.listeners.delete(subscriptionKey)
    console.log(`🧹 Cleaned up subscription: ${subscriptionKey}`)
  }

  /**
   * Cleanup all subscriptions
   */
  cleanupAll() {
    console.log('🧹 Cleaning up all real-time subscriptions')
    this.subscriptions.forEach((subscription, key) => {
      subscription.unsubscribe()
    })
    this.subscriptions.clear()
    this.listeners.clear()
  }

  /**
   * Get active subscription count (for debugging)
   */
  getActiveSubscriptions(): string[] {
    return Array.from(this.subscriptions.keys())
  }
}

// Export singleton instance
export const realtimeManager = new RealtimeManager()

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    realtimeManager.cleanupAll()
  })
}
