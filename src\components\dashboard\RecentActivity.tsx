import React from 'react';
import { motion } from 'framer-motion';
import { Icons } from '@/components/ui/icons';
import { Button } from '@/components/ui/button';

interface RecentBook {
  id: string;
  title: string;
  author: string;
  progress_percentage: number;
  last_read_at: string;
  is_favorited: boolean;
  summaries: {
    estimated_reading_time: number;
    books: {
      title: string;
      author: string;
      cover_image_url?: string;
    };
  };
}

interface RecentActivityProps {
  recentBooks: RecentBook[];
  onContinueReading: (bookId: string) => void;
}

export function RecentActivity({ recentBooks, onContinueReading }: RecentActivityProps) {
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Agora mesmo';
    if (diffInHours < 24) return `${diffInHours}h atrás`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d atrás`;
    return date.toLocaleDateString('pt-BR');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="bg-white rounded-2xl border border-gray-100 p-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <h2 className="text-xl font-bold text-gray-900">Continue Lendo</h2>
          <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
            <Icons.Clock className="w-4 h-4 text-blue-600" />
          </div>
        </div>
        <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
          <Icons.MoreHorizontal className="w-4 h-4" />
        </Button>
      </div>

      {/* Recent Books */}
      <div className="space-y-4">
        {recentBooks.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Icons.BookOpen className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Comece sua jornada
            </h3>
            <p className="text-gray-500 text-sm mb-4">
              Explore nossa biblioteca e comece a ler seus primeiros resumos.
            </p>
            <Button className="bg-gray-900 text-white hover:bg-gray-800">
              Explorar Biblioteca
            </Button>
          </div>
        ) : (
          recentBooks.map((book, index) => (
            <motion.div
              key={book.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="group relative"
            >
              <div className="flex items-center space-x-4 p-4 rounded-xl hover:bg-gray-50 transition-all duration-300 cursor-pointer">
                {/* Book Cover */}
                <div className="relative w-16 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center flex-shrink-0 overflow-hidden group-hover:scale-105 transition-transform duration-300">
                  {book.summaries?.books?.cover_image_url ? (
                    <img 
                      src={book.summaries.books.cover_image_url} 
                      alt={book.summaries.books.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Icons.BookOpen className="w-8 h-8 text-gray-400" />
                  )}
                  
                  {/* Icons.Play Overlay */}
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg">
                      <Icons.Play className="w-4 h-4 text-gray-900 ml-0.5" />
                    </div>
                  </div>
                </div>

                {/* Book Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold text-gray-900 text-sm truncate">
                          {book.summaries?.books?.title || book.title}
                        </h3>
                        {book.is_favorited && (
                          <Icons.Star className="w-4 h-4 text-yellow-500 fill-current flex-shrink-0" />
                        )}
                      </div>
                      <p className="text-xs text-gray-500 mb-3">
                        {book.summaries?.books?.author || book.author}
                      </p>
                    </div>
                    
                    <div className="text-right flex-shrink-0 ml-4">
                      <span className="text-xs text-gray-400 block mb-1">
                        {formatTimeAgo(book.last_read_at)}
                      </span>
                      <span className="text-xs font-medium text-gray-600">
                        {Math.round(book.progress_percentage)}% concluído
                      </span>
                    </div>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <motion.div 
                        className="bg-gradient-to-r from-gray-900 to-gray-700 h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${book.progress_percentage}%` }}
                        transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
                      />
                    </div>
                  </div>

                  {/* Action Button */}
                  <Button
                    size="sm"
                    onClick={() => onContinueReading(book.id)}
                    className="opacity-0 group-hover:opacity-100 transition-all duration-300 bg-gray-900 text-white hover:bg-gray-800 text-xs px-4 py-2 h-8"
                  >
                    Continuar Lendo
                  </Button>
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>

      {/* View All */}
      {recentBooks.length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-100">
          <Button variant="ghost" className="w-full text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50">
            Ver todo o histórico
          </Button>
        </div>
      )}
    </motion.div>
  );
}