import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { BookLoader } from '@/lib/bookLoader';
import EdgeCompatiblePDFViewer from '../EdgeCompatiblePDFViewer';
import { Icons } from '@/components/ui/icons';
import { useReadingProgress, useReadingSequence, useReadingSession } from '@/hooks/useReadingProgress';
import { dataService } from '@/lib/dataService';
import './reader.css';

interface PDFReaderProps {
  bookId: string;
  bookTitle: string;
  bookAuthor: string;
  content: string;
  onClose: () => void;
}

export function PDFReader({ bookId, bookTitle, bookAuthor, content, onClose }: PDFReaderProps) {
  // Reading progress hooks
  const { progressState, updateProgress, toggleFavorite } = useReadingProgress(bookId);
  const { preferences, updatePreferences } = useReadingSequence();
  const { session, startSession, updateSession, endSession } = useReadingSession(bookId);

  // Progress state
  const [progress, setProgress] = useState(0);

  // Local state
  const [fontSize, setFontSize] = useState(preferences.fontSize === 'small' ? 14 : preferences.fontSize === 'large' ? 18 : 16);
  const [showControls, setShowControls] = useState(true);
  const [bookContent, setBookContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEmbeddedPDF, setIsEmbeddedPDF] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [contentChunks, setContentChunks] = useState<string[]>([]);

  // Initialize reading session when content loads
  useEffect(() => {
    if (bookContent && !session) {
      // Split content into chunks for better performance
      const chunkSize = 2000; // ~2000 chars per page
      const chunks = [];
      for (let i = 0; i < bookContent.length; i += chunkSize) {
        chunks.push(bookContent.slice(i, i + chunkSize));
      }

      setContentChunks(chunks);
      const estimatedPages = Math.max(1, chunks.length);
      setTotalPages(estimatedPages);
      startSession(estimatedPages);

      // Load saved page position
      loadSavedPage();
      setLoading(false);
    }
  }, [bookContent, session, startSession]);

  // Load saved page position
  const loadSavedPage = async () => {
    try {
      const savedPosition = await dataService.getCurrentPage(bookId);
      if (savedPosition) {
        setCurrentPage(savedPosition.currentPage);
        setTotalPages(savedPosition.totalPages);
      }
    } catch (error) {
      console.error('Error loading saved page:', error);
    }
  };

  // Update progress when current page changes
  useEffect(() => {
    if (totalPages > 0) {
      const progressPercentage = Math.round((currentPage / totalPages) * 100);
      updateSession(currentPage);

      // Save current page position
      saveCurrentPagePosition();

      // Only update progress if there's meaningful change (avoid marking as complete on first load)
      const currentProgressState = progressState?.progress || 0;
      const progressDifference = Math.abs(progressPercentage - currentProgressState);

      // Update progress in database every 10% increment or when reaching 100%
      if ((progressPercentage % 10 === 0 && progressDifference >= 10) ||
          (progressPercentage === 100 && currentPage === totalPages)) {
        updateProgress(progressPercentage);
      }
    }
  }, [currentPage, totalPages, updateSession, updateProgress, progressState]);

  // Save current page position
  const saveCurrentPagePosition = async () => {
    try {
      await dataService.saveCurrentPage(bookId, currentPage, totalPages);
    } catch (error) {
      console.error('Error saving current page:', error);
    }
  };

  // Load book content
  useEffect(() => {
    const loadContent = async () => {
      console.log('PDFReader: Loading content for book ID:', bookId);
      setLoading(true);
      setError(null);

      try {
        const text = await BookLoader.getBookText(bookId);
        console.log('PDFReader: Content loaded, length:', text.length);

        // Check if this is an embedded PDF
        const isPDF = text.includes('<PDF_VIEWER_PLACEHOLDER>');
        setIsEmbeddedPDF(isPDF);

        setBookContent(text);
      } catch (error) {
        console.error('PDFReader: Error loading book content:', error);
        setError('Erro ao carregar o conteúdo do livro.');
        setBookContent('Erro ao carregar o conteúdo do livro. Tente novamente mais tarde.');
      } finally {
        setLoading(false);
      }
    };

    if (bookId) {
      loadContent();
    }
  }, [bookId]);

  // Handle reader close with progress saving
  const handleClose = async () => {
    console.log('PDFReader: handleClose called');

    // Close immediately to ensure responsiveness
    onClose();

    // Save progress in background (don't await to avoid blocking)
    try {
      // Save current page position before closing
      if (currentPage && totalPages) {
        dataService.saveCurrentPage(bookId, currentPage, totalPages).catch(error => {
          console.error('Error saving current page:', error);
        });
      }

      // End reading session
      if (session) {
        endSession().catch(error => {
          console.error('Error ending session:', error);
        });
      }
    } catch (error) {
      console.error('Error in background save:', error);
    }
  };

  // Handle favorite toggle
  const handleToggleFavorite = async () => {
    await toggleFavorite();
  };

  // Handle font size changes
  const handleFontSizeChange = (newSize: number) => {
    setFontSize(newSize);
    const sizeCategory = newSize <= 14 ? 'small' : newSize >= 18 ? 'large' : 'medium';
    updatePreferences({ fontSize: sizeCategory });
  };

  // Hide controls after 4 seconds of inactivity (improved UX)
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowControls(false);
    }, 4000);

    return () => clearTimeout(timer);
  }, [showControls]);

  // Calculate reading progress based on scroll
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = docHeight > 0 ? (scrollTop / docHeight) * 100 : 0;
      setProgress(Math.min(100, Math.max(0, scrollPercent)));
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const scrollAmount = window.innerHeight * 0.8; // 80% da altura da tela

      switch (event.key) {
        case 'ArrowDown':
        case 'PageDown':
        case ' ': // Spacebar
          event.preventDefault();
          window.scrollBy({ top: scrollAmount, behavior: 'smooth' });
          setShowControls(true);
          break;
        case 'ArrowUp':
        case 'PageUp':
          event.preventDefault();
          window.scrollBy({ top: -scrollAmount, behavior: 'smooth' });
          setShowControls(true);
          break;
        case 'Icons.Home':
          event.preventDefault();
          window.scrollTo({ top: 0, behavior: 'smooth' });
          setShowControls(true);
          break;
        case 'End':
          event.preventDefault();
          window.scrollTo({ top: document.documentElement.scrollHeight, behavior: 'smooth' });
          setShowControls(true);
          break;
        case 'Escape':
          console.log('PDFReader: Escape key pressed');
          handleClose();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleClose]);

  // Simplified theme functions - light theme only
  const getThemeClasses = () => 'bg-white';
  const getControlsTheme = () => 'hover:bg-gray-100 text-gray-900';
  const getProgressBarTheme = () => 'bg-white text-gray-900';

  if (loading) {
    return (
      <div className="fixed inset-0 z-50 bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-gray-300 border-t-gray-600 rounded-full animate-spin mb-4"></div>
          <p className="text-lg font-medium text-gray-900">Carregando livro...</p>
          <p className="text-sm mt-2 text-gray-500">{bookTitle}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 z-50 bg-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-red-600 text-2xl">⚠️</span>
          </div>
          <h3 className="text-lg font-semibold mb-2 text-gray-900">Erro ao Carregar</h3>
          <p className="mb-4 text-gray-600">{error}</p>
          <div className="space-y-2">
            <Button onClick={handleClose} variant="outline" className="w-full">
              Voltar
            </Button>
            <Button
              onClick={() => window.location.reload()}
              className="w-full bg-gray-900 text-white hover:bg-gray-800"
            >
              Tentar Novamente
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`fixed inset-0 z-50 ${getThemeClasses()} transition-colors duration-300`}
      style={{
        overflowY: 'auto',
        overflowX: 'hidden',
        scrollbarWidth: 'thin',
        scrollbarColor: '#d1d5db #f9fafb'
      }}
      onMouseMove={() => setShowControls(true)}
      onWheel={() => setShowControls(true)}
      onClick={() => setShowControls(true)}
      tabIndex={0} // Permite foco para capturar eventos de teclado
    >
      {/* Enhanced Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-2 bg-gray-200 z-50 cursor-pointer"
           onClick={(e) => {
             const rect = e.currentTarget.getBoundingClientRect();
             const clickX = e.clientX - rect.left;
             const percentage = (clickX / rect.width) * 100;
             const docHeight = document.documentElement.scrollHeight - window.innerHeight;
             const targetScroll = (percentage / 100) * docHeight;
             window.scrollTo({ top: targetScroll, behavior: 'smooth' });
           }}
           title={`${Math.round(progressState?.progress || 0)}% concluído - Clique para navegar`}
      >
        <motion.div
          className="h-full bg-gradient-to-r from-gray-800 to-gray-900 shadow-sm"
          style={{ width: `${progressState?.progress || 0}%` }}
          transition={{ duration: 0.2 }}
        />
      </div>

      {/* Top Controls */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="fixed top-4 left-4 right-4 z-40"
          >
            <div className="flex items-center justify-between px-4 py-3 rounded-xl bg-white/95 backdrop-blur-md shadow-lg border border-gray-200">
              <Button
                onClick={handleClose}
                variant="ghost"
                className="p-2 rounded-full hover:bg-gray-100 text-gray-700 transition-colors"
                title="Voltar à biblioteca"
              >
                <Icons.ArrowLeft className="w-5 h-5" />
              </Button>

              <div className="flex-1 mx-4">
                <h1 className="text-base font-medium text-center truncate text-gray-900">
                  {bookTitle}
                </h1>
                {/* Progress indicator */}
                {progressState && (
                  <div className="text-xs text-gray-500 text-center mt-1">
                    {progressState.progress}% concluído
                  </div>
                )}
              </div>

              {/* Favorite button */}
              <button
                onClick={handleToggleFavorite}
                className={`w-8 h-8 flex items-center justify-center rounded-full border transition-all duration-200 shadow-sm mr-2 ${
                  progressState?.isFavorited
                    ? 'bg-red-50 hover:bg-red-100 border-red-200 text-red-600'
                    : 'bg-white/80 hover:bg-white border-gray-200 text-gray-400 hover:text-gray-600'
                }`}
                title={progressState?.isFavorited ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
              >
                <Icons.Heart className={`w-4 h-4 ${progressState?.isFavorited ? 'fill-current' : ''}`} />
              </button>

              {/* Emergency close button */}
              <button
                onClick={() => {
                  console.log('PDFReader: Emergency close button clicked');
                  onClose();
                }}
                className="w-8 h-8 flex items-center justify-center rounded-full bg-white/80 hover:bg-white border border-gray-200 text-gray-400 hover:text-gray-600 transition-all duration-200 shadow-sm mr-2"
                title="Fechar leitor"
              >
                <Icons.X className="w-4 h-4" />
              </button>

              {/* Clean Zoom Controls */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleFontSizeChange(Math.max(12, fontSize - 1))}
                  className="w-8 h-8 flex items-center justify-center rounded-full bg-white/80 hover:bg-white border border-gray-200 text-gray-700 hover:text-gray-900 transition-all duration-200 shadow-sm"
                  title="Diminuir zoom"
                >
                  <span className="text-lg font-medium leading-none">−</span>
                </button>

                <span className="text-sm font-medium text-gray-900 min-w-[2.5rem] text-center">
                  {fontSize}pt
                </span>

                <button
                  onClick={() => handleFontSizeChange(Math.min(28, fontSize + 1))}
                  className="w-8 h-8 flex items-center justify-center rounded-full bg-white/80 hover:bg-white border border-gray-200 text-gray-700 hover:text-gray-900 transition-all duration-200 shadow-sm"
                  title="Aumentar zoom"
                >
                  <span className="text-lg font-medium leading-none">+</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Subtle hint when controls are hidden */}
      <AnimatePresence>
        {!showControls && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ delay: 2 }}
            className="fixed top-6 left-1/2 transform -translate-x-1/2 z-30"
          >
            <div className="px-3 py-1 bg-gray-900/10 backdrop-blur-sm rounded-full border border-gray-200/50">
              <span className="text-xs text-gray-500">Mova o mouse para mostrar controles</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content - Kindle-style formatting handled by CSS */}
      <div className="pt-20 pb-20 min-h-screen">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full"
        >
          {isEmbeddedPDF ? (
            // Render Edge-compatible PDF viewer with font size as zoom
            <div className="w-full">
              <EdgeCompatiblePDFViewer bookId={bookId} className="w-full" fontSize={fontSize} />
            </div>
          ) : (
            // Render current page chunk for better performance
            <div
              className="formatted-content light"
              dangerouslySetInnerHTML={{
                __html: BookLoader.formatTextForReader(
                  contentChunks[currentPage - 1] || bookContent,
                  'light'
                )
              }}
              style={{
                fontSize: `${fontSize}pt`
              }}
            />
          )}
        </motion.div>
      </div>


    </motion.div>
  );
}