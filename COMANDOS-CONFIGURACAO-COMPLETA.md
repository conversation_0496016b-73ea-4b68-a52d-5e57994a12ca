# 🚀 CONFIGURAÇÃO COMPLETA - COMANDOS PARA EXECUTAR

## 📋 **PASSO A PASSO COMPLETO**

### **1. 🔑 Obter Service Role Key**

1. **Acesse o Dashboard:**
   ```
   https://supabase.com/dashboard/project/qmeelujsnpbcdkzhcwmm
   ```

2. **Navegue para Settings → API**

3. **Copie a "service_role" key** (clique no olho para revelar)

---

### **2. 🔧 Executar Comandos de Configuração**

Execute estes comandos **UM POR VEZ** no terminal:

```bash
# 1. Login no Supabase (abrirá o navegador)
npx supabase login

# 2. Link do projeto
npx supabase link --project-ref qmeelujsnpbcdkzhcwmm

# 3. Configurar Stripe Secret Key
npx supabase secrets set STRIPE_SECRET_KEY="sk_test_51RIcDWQXmlk54RXco22WsOUYY2jtE27RhFyvbaaiInFpIDD5yFcbQNPjSilbv6pzKxq1YuihBgAy4ria1OV3l2mY00gw9ED5fy"

# 4. Configurar Stripe Webhook Secret
npx supabase secrets set STRIPE_WEBHOOK_SECRET="whsec_rSOSlhBwwbkRSVfgqla96EctF9NTcL9c"

# 5. Configurar Supabase URL
npx supabase secrets set SUPABASE_URL="https://qmeelujsnpbcdkzhcwmm.supabase.co"

# 6. Configurar Supabase Anon Key
npx supabase secrets set SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4"

# 7. Configurar Service Role Key (SUBSTITUA pela chave real)
npx supabase secrets set SUPABASE_SERVICE_ROLE_KEY="SUA_SERVICE_ROLE_KEY_AQUI"

# 8. Verificar secrets configurados
npx supabase secrets list
```

---

### **3. 🚀 Deploy das Edge Functions**

```bash
# Deploy stripe-checkout
npx supabase functions deploy stripe-checkout

# Deploy stripe-webhook  
npx supabase functions deploy stripe-webhook

# Deploy stripe-portal
npx supabase functions deploy stripe-portal

# Listar functions deployadas
npx supabase functions list
```

---

### **4. 📊 Verificar Deploy**

```bash
# Ver logs da função webhook
npx supabase functions logs stripe-webhook

# Ver logs da função checkout
npx supabase functions logs stripe-checkout

# Status geral do projeto
npx supabase status
```

---

## 🎯 **RESULTADO ESPERADO**

Após executar todos os comandos, você deve ver:

### **✅ Secrets Configurados:**
- STRIPE_SECRET_KEY
- STRIPE_WEBHOOK_SECRET  
- SUPABASE_URL
- SUPABASE_ANON_KEY
- SUPABASE_SERVICE_ROLE_KEY

### **✅ Functions Deployadas:**
- stripe-checkout
- stripe-webhook
- stripe-portal

### **✅ URLs Funcionais:**
- `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-checkout`
- `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-webhook`
- `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal`

---

## 🧪 **TESTAR INTEGRAÇÃO**

### **1. Testar Checkout Function:**
```bash
curl -X POST https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-checkout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4" \
  -d '{
    "price_id": "price_1Rov58QXmlk54RXcFlW7ymIL",
    "user_id": "test-user-123",
    "success_url": "http://localhost:5173/subscription/success",
    "cancel_url": "http://localhost:5173/subscription/cancel",
    "plan_id": "premium"
  }'
```

### **2. Testar na Aplicação:**
1. Inicie sua aplicação: `npm run dev`
2. Navegue para seção "Assinatura"
3. Tente fazer upgrade para Premium
4. Monitore logs: `npx supabase functions logs stripe-webhook --follow`

---

## ⚠️ **TROUBLESHOOTING**

### **Erro de Login:**
```bash
npx supabase logout
npx supabase login
```

### **Erro de Link:**
```bash
npx supabase unlink
npx supabase link --project-ref qmeelujsnpbcdkzhcwmm
```

### **Erro de Deploy:**
```bash
# Verificar status
npx supabase status

# Ver logs detalhados
npx supabase functions logs stripe-checkout --follow
```

### **Reconfigurar Secret:**
```bash
# Remover secret
npx supabase secrets unset STRIPE_SECRET_KEY

# Reconfigurar
npx supabase secrets set STRIPE_SECRET_KEY="nova_chave_aqui"
```

---

## 🎉 **APÓS CONFIGURAÇÃO COMPLETA**

1. **✅ Webhook do Stripe já configurado**
2. **✅ Edge Functions deployadas**  
3. **✅ Secrets configurados**
4. **✅ Integração pronta para uso**

### **Monitoramento:**
- Dashboard Supabase: https://supabase.com/dashboard/project/qmeelujsnpbcdkzhcwmm
- Dashboard Stripe: https://dashboard.stripe.com/test/webhooks
- Logs em tempo real: `npx supabase functions logs stripe-webhook --follow`

**Execute os comandos acima e sua integração Stripe + Supabase estará 100% funcional!** 🚀
