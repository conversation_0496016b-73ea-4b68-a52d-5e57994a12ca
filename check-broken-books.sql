-- Verificar outros livros com problemas similares
-- Execute este SQL no Supabase Dashboard > SQL Editor

-- STEP 1: Livros sem PDF_KEY mas que podem ter PDF no Storage
SELECT 'LIVROS SEM PDF_KEY' as check_type;

SELECT 
    id,
    title,
    author,
    category,
    pdf_key,
    description,
    created_at
FROM books 
WHERE (pdf_key IS NULL OR pdf_key = '')
  AND is_featured = true
ORDER BY created_at DESC
LIMIT 10;

-- STEP 2: Livros que mostram conteúdo placeholder
SELECT 'LIVROS COM CONTEÚDO PLACEHOLDER' as check_type;

SELECT 
    b.id,
    b.title,
    b.author,
    b.pdf_key,
    CASE 
        WHEN bc.content::text LIKE '%conteúdo completo está sendo processado%' THEN '❌ PLACEHOLDER'
        WHEN bc.content::text LIKE '%Nossa equipe está trabalhando%' THEN '❌ PLACEHOLDER'
        WHEN bc.content IS NULL THEN '⚠️ SEM CONTEÚDO'
        ELSE '✅ CONTEÚDO REAL'
    END as content_type,
    LENGTH(bc.content::text) as content_length
FROM books b
LEFT JOIN book_contents bc ON bc.book_id = b.id
WHERE b.is_featured = true
  AND (bc.content IS NULL 
       OR bc.content::text LIKE '%conteúdo completo está sendo processado%'
       OR bc.content::text LIKE '%Nossa equipe está trabalhando%')
ORDER BY b.created_at DESC;

-- STEP 3: Livros que têm PDF_KEY mas podem estar quebrados
SELECT 'LIVROS COM PDF_KEY PARA VERIFICAR' as check_type;

SELECT 
    id,
    title,
    author,
    pdf_key,
    'https://qmeelujsnpbcdkzhcwmm.supabase.co/storage/v1/object/public/books/' || pdf_key as test_url,
    created_at
FROM books 
WHERE pdf_key IS NOT NULL 
  AND pdf_key != ''
  AND is_featured = true
ORDER BY created_at DESC
LIMIT 10;

-- STEP 4: Estatísticas gerais
SELECT 'ESTATÍSTICAS GERAIS' as check_type;

SELECT 
    'Total de livros' as metric,
    COUNT(*) as count
FROM books

UNION ALL

SELECT 
    'Livros em destaque' as metric,
    COUNT(*) as count
FROM books 
WHERE is_featured = true

UNION ALL

SELECT 
    'Livros com PDF_KEY' as metric,
    COUNT(*) as count
FROM books 
WHERE pdf_key IS NOT NULL AND pdf_key != ''

UNION ALL

SELECT 
    'Livros sem PDF_KEY' as metric,
    COUNT(*) as count
FROM books 
WHERE pdf_key IS NULL OR pdf_key = ''

UNION ALL

SELECT 
    'Livros com conteúdo estruturado' as metric,
    COUNT(*) as count
FROM books b
JOIN book_contents bc ON bc.book_id = b.id
WHERE bc.content IS NOT NULL

UNION ALL

SELECT 
    'Livros com conteúdo placeholder' as metric,
    COUNT(*) as count
FROM books b
JOIN book_contents bc ON bc.book_id = b.id
WHERE bc.content::text LIKE '%conteúdo completo está sendo processado%'
   OR bc.content::text LIKE '%Nossa equipe está trabalhando%';

-- STEP 5: Livros que precisam de correção urgente
SELECT 'LIVROS QUE PRECISAM DE CORREÇÃO' as check_type;

SELECT 
    b.id,
    b.title,
    b.author,
    b.is_featured,
    CASE 
        WHEN b.pdf_key IS NULL OR b.pdf_key = '' THEN '❌ SEM PDF_KEY'
        ELSE '✅ TEM PDF_KEY'
    END as pdf_status,
    CASE 
        WHEN bc.content IS NULL THEN '❌ SEM CONTEÚDO'
        WHEN bc.content::text LIKE '%conteúdo completo está sendo processado%' THEN '❌ PLACEHOLDER'
        ELSE '✅ CONTEÚDO OK'
    END as content_status,
    CASE 
        WHEN (b.pdf_key IS NULL OR b.pdf_key = '') AND 
             (bc.content IS NULL OR bc.content::text LIKE '%conteúdo completo está sendo processado%') 
        THEN '🚨 CRÍTICO'
        WHEN (b.pdf_key IS NULL OR b.pdf_key = '') OR 
             (bc.content IS NULL OR bc.content::text LIKE '%conteúdo completo está sendo processado%')
        THEN '⚠️ PRECISA CORREÇÃO'
        ELSE '✅ OK'
    END as priority
FROM books b
LEFT JOIN book_contents bc ON bc.book_id = b.id
WHERE b.is_featured = true
ORDER BY 
    CASE 
        WHEN (b.pdf_key IS NULL OR b.pdf_key = '') AND 
             (bc.content IS NULL OR bc.content::text LIKE '%conteúdo completo está sendo processado%') 
        THEN 1
        WHEN (b.pdf_key IS NULL OR b.pdf_key = '') OR 
             (bc.content IS NULL OR bc.content::text LIKE '%conteúdo completo está sendo processado%')
        THEN 2
        ELSE 3
    END,
    b.title;

-- STEP 6: Sugestões de correção
SELECT 'SUGESTÕES DE CORREÇÃO' as check_type;

SELECT 
    'Para livros sem PDF_KEY:' as suggestion,
    '1. Verificar se existe PDF no Storage Supabase' as action_1,
    '2. Atualizar campo pdf_key com o nome correto do arquivo' as action_2,
    '3. Remover conteúdo placeholder da tabela book_contents' as action_3

UNION ALL

SELECT 
    'Para livros com conteúdo placeholder:' as suggestion,
    '1. Verificar se existe PDF correspondente' as action_1,
    '2. Se existe PDF, remover conteúdo placeholder' as action_2,
    '3. Se não existe PDF, adicionar conteúdo real estruturado' as action_3

UNION ALL

SELECT 
    'Para livros críticos (sem PDF e sem conteúdo):' as suggestion,
    '1. Procurar PDF original do livro' as action_1,
    '2. Fazer upload para Storage Supabase' as action_2,
    '3. Atualizar pdf_key ou adicionar conteúdo estruturado' as action_3;
