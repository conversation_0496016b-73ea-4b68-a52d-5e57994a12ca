import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Paths
const PDF_SOURCE = path.join(__dirname, 'resumos_padronizados_roboto_final', 'home', 'ubuntu', 'resumos_padronizados', 'pdf_final');
const PDF_DEST = path.join(__dirname, 'public', 'pdfs');

async function processarTodosPDFsRestantes() {
  console.log('📁 PROCESSANDO TODOS OS PDFs RESTANTES...');
  console.log('🎯 Objetivo: Copiar TODOS os PDFs para máxima cobertura');
  
  try {
    // Get all PDF files from source
    if (!fs.existsSync(PDF_SOURCE)) {
      console.log('❌ Pasta de origem não encontrada:', PDF_SOURCE);
      return;
    }
    
    const allSourcePDFs = fs.readdirSync(PDF_SOURCE).filter(file => file.endsWith('.pdf'));
    console.log(`📄 Encontrados ${allSourcePDFs.length} PDFs na pasta de origem`);
    
    // Get existing PDFs in destination
    const existingPDFs = fs.existsSync(PDF_DEST) ? 
      fs.readdirSync(PDF_DEST).filter(file => file.endsWith('.pdf')) : [];
    console.log(`📁 Já existem ${existingPDFs.length} PDFs na pasta de destino`);
    
    // Find PDFs that need to be copied
    const newPDFs = allSourcePDFs.filter(pdf => !existingPDFs.includes(pdf));
    console.log(`🆕 ${newPDFs.length} novos PDFs para copiar`);
    
    if (newPDFs.length === 0) {
      console.log('✅ Todos os PDFs já foram copiados!');
      console.log(`📊 Total de PDFs disponíveis: ${existingPDFs.length}`);
      return;
    }
    
    // Ensure destination directory exists
    if (!fs.existsSync(PDF_DEST)) {
      fs.mkdirSync(PDF_DEST, { recursive: true });
      console.log('✅ Pasta public/pdfs criada');
    }
    
    console.log('\n📋 COPIANDO NOVOS PDFs...');
    
    let copiedCount = 0;
    let errorCount = 0;
    let totalSize = 0;
    
    for (const pdfFile of newPDFs) {
      try {
        const sourcePath = path.join(PDF_SOURCE, pdfFile);
        const destPath = path.join(PDF_DEST, pdfFile);
        
        // Copy file
        fs.copyFileSync(sourcePath, destPath);
        
        // Get file size
        const stats = fs.statSync(destPath);
        const sizeKB = Math.round(stats.size / 1024);
        totalSize += stats.size;
        
        console.log(`✅ ${pdfFile} (${sizeKB} KB)`);
        copiedCount++;
        
        // Small delay to avoid overwhelming the system
        if (copiedCount % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
      } catch (error) {
        console.log(`❌ Erro ao copiar ${pdfFile}: ${error.message}`);
        errorCount++;
      }
    }
    
    // Final summary
    const totalSizeMB = Math.round(totalSize / (1024 * 1024));
    const finalPDFCount = existingPDFs.length + copiedCount;
    
    console.log('\n📊 RESULTADO FINAL:');
    console.log(`✅ Novos PDFs copiados: ${copiedCount}`);
    console.log(`❌ Erros: ${errorCount}`);
    console.log(`📁 Total de PDFs disponíveis: ${finalPDFCount}`);
    console.log(`💾 Tamanho total adicionado: ${totalSizeMB} MB`);
    
    if (copiedCount > 0) {
      console.log('\n🎉 SUCESSO! Mais PDFs adicionados à biblioteca!');
      
      // Calculate potential coverage improvement
      console.log('\n📈 IMPACTO NA COBERTURA:');
      console.log(`📄 PDFs disponíveis antes: ${existingPDFs.length}`);
      console.log(`📄 PDFs disponíveis agora: ${finalPDFCount}`);
      
      const improvementPercent = ((copiedCount / existingPDFs.length) * 100);
      console.log(`📊 Melhoria: +${improvementPercent.toFixed(1)}% mais PDFs`);
      
      console.log('\n🔄 PRÓXIMO PASSO:');
      console.log('Execute: node verificar-cobertura-completa.js');
      console.log('Para ver a nova cobertura de PDFs na biblioteca');
      
    } else if (errorCount === 0) {
      console.log('\n✅ Todos os PDFs já estavam disponíveis!');
      console.log('📚 Biblioteca está com cobertura máxima de PDFs');
    }
    
  } catch (error) {
    console.error('❌ Processamento falhou:', error.message);
  }
}

// Run processing
processarTodosPDFsRestantes().catch(console.error);
