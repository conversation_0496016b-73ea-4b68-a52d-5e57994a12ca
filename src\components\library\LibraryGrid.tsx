import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { BookCard } from './BookCard';
import { Icons } from '@/components/ui/icons';
import { Button } from '@/components/ui/button';

interface Book {
  id: string | number;
  title: string;
  author: string;
  category: string;
  duration: number;
  difficulty: string;
  is_featured: boolean;
  description: string;
  cover_image_url?: string;
}

interface LibraryGridProps {
  books: Book[];
  onReadBook: (bookId: string | number) => void;
  onFavoriteBook?: (bookId: string | number) => void;
  favoriteBooks?: (string | number)[];
  isLoading?: boolean;
}

export function LibraryGrid({ 
  books, 
  onReadBook, 
  onFavoriteBook, 
  favoriteBooks = [],
  isLoading = false 
}: LibraryGridProps) {
  const [filteredBooks, setFilteredBooks] = useState(books);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('featured');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const categories = ['all', ...Array.from(new Set(books.map(book => book.category)))];

  useEffect(() => {
    let filtered = books;

    // Icons.Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(book => book.category === selectedCategory);
    }

    // Sort books
    switch (sortBy) {
      case 'featured':
        filtered = filtered.sort((a, b) => {
          if (a.is_featured && !b.is_featured) return -1;
          if (!a.is_featured && b.is_featured) return 1;
          return 0;
        });
        break;
      case 'title':
        filtered = filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'author':
        filtered = filtered.sort((a, b) => a.author.localeCompare(b.author));
        break;
      case 'duration':
        filtered = filtered.sort((a, b) => a.duration - b.duration);
        break;
    }

    setFilteredBooks(filtered);
  }, [books, selectedCategory, sortBy]);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="bg-white rounded-2xl border border-gray-100 overflow-hidden animate-pulse">
            <div className="h-48 bg-gray-200" />
            <div className="p-5 space-y-3">
              <div className="h-4 bg-gray-200 rounded w-1/3" />
              <div className="h-5 bg-gray-200 rounded w-3/4" />
              <div className="h-4 bg-gray-200 rounded w-1/2" />
              <div className="h-3 bg-gray-200 rounded w-full" />
              <div className="h-3 bg-gray-200 rounded w-2/3" />
              <div className="flex justify-between items-center">
                <div className="h-4 bg-gray-200 rounded w-16" />
                <div className="h-8 bg-gray-200 rounded w-20" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters and Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-4">
          {/* Category Icons.Filter */}
          <div className="flex items-center space-x-2">
            <Icons.Filter className="w-4 h-4 text-gray-500" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-gray-900 focus:border-transparent"
            >
              <option value="all">Todas as categorias</option>
              {categories.slice(1).map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          {/* Sort */}
          <div className="flex items-center space-x-2">
            <Icons.SortAsc className="w-4 h-4 text-gray-500" />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-gray-900 focus:border-transparent"
            >
              <option value="featured">Destaques</option>
              <option value="title">Título</option>
              <option value="author">Autor</option>
              <option value="duration">Duração</option>
            </select>
          </div>
        </div>

        {/* View Mode */}
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('grid')}
            className="p-2"
          >
            <Icons.Grid className="w-4 h-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="p-2"
          >
            <Icons.List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Results Count */}
      <div className="text-sm text-gray-500">
        {filteredBooks.length} {filteredBooks.length === 1 ? 'resumo encontrado' : 'resumos encontrados'}
      </div>

      {/* Books Icons.Grid */}
      <motion.div
        layout
        className={`grid gap-6 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
            : 'grid-cols-1'
        }`}
      >
        {filteredBooks.map((book, index) => (
          <motion.div
            key={book.id}
            layout
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <BookCard
              book={book}
              onRead={onReadBook}
              onFavorite={onFavoriteBook}
              isFavorited={favoriteBooks.includes(String(book.id))}
            />
          </motion.div>
        ))}
      </motion.div>

      {/* Empty State */}
      {filteredBooks.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-16"
        >
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icons.Filter className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Nenhum resumo encontrado
          </h3>
          <p className="text-gray-500 mb-4">
            Tente ajustar os filtros ou buscar por outros termos.
          </p>
          <Button
            onClick={() => {
              setSelectedCategory('all');
              setSortBy('featured');
            }}
            variant="outline"
          >
            Limpar filtros
          </Button>
        </motion.div>
      )}
    </div>
  );
}