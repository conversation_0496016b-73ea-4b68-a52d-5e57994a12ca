# Book Summaries Platform - API RESTful

Uma plataforma completa de resumos de livros com sistema de assinaturas e controle de acesso.

## 🚀 Funcionalidades

### Sistema de Acesso
- **Gratuito**: Primeiro mês com acesso total, depois apenas 2 resumos mensais
- **Premium (R$15/mês)**: Acesso ilimitado a todos os resumos

### API Endpoints

#### Resumos (`/functions/v1/summaries`)
- `GET /summaries` - Lista resumos com paginação e filtros
- `GET /summaries/:id` - Obtém resumo específico (requer autenticação)
- `GET /summaries/categories` - Lista todas as categorias
- `GET /summaries/free-monthly` - Resumos gratuitos do mês atual

#### Progresso do Usuário (`/functions/v1/user-progress`)
- `GET /user-progress/dashboard` - Dashboard completo do usuário
- `GET /user-progress/summary/:id` - Progresso específico de um resumo
- `POST /user-progress` - Atualiza progresso de leitura
- `PUT /user-progress/favorite/:id` - Toggle favorito

#### Assinaturas (`/functions/v1/subscriptions`)
- `GET /subscriptions/current` - Assinatura atual do usuário
- `POST /subscriptions/create-checkout-session` - Cria sessão de pagamento Stripe
- `DELETE /subscriptions/cancel` - Cancela assinatura
- `PUT /subscriptions/webhook` - Webhook do Stripe

## 🛠️ Tecnologias

- **Backend**: Supabase (Database + Edge Functions)
- **Pagamentos**: Stripe
- **Autenticação**: Supabase Auth
- **Database**: PostgreSQL com RLS
- **API**: RESTful com TypeScript

## 📊 Estrutura do Banco

### Tabelas Principais
- `books` - Informações dos livros
- `summaries` - Resumos dos livros
- `categories` - Categorias dos livros
- `user_subscriptions` - Controle de assinaturas
- `user_reading_progress` - Progresso de leitura
- `monthly_free_summaries` - Resumos gratuitos mensais
- `user_summary_access` - Log de acessos

### Segurança
- Row Level Security (RLS) habilitado
- Políticas específicas por tipo de usuário
- Controle de acesso baseado em assinatura

## 🔧 Configuração

1. **Supabase Setup**
   ```bash
   # Execute as migrações
   supabase db push
   ```

2. **Variáveis de Ambiente**
   ```bash
   cp .env.example .env
   # Configure suas chaves do Supabase e Stripe
   ```

3. **Stripe Configuration**
   - Configure webhooks para `/functions/v1/subscriptions/webhook`
   - Eventos necessários: `checkout.session.completed`, `customer.subscription.updated`, `customer.subscription.deleted`

## 📈 Funcionalidades Avançadas

### Sistema de Cache
- Cache automático do Supabase
- Otimização de queries com índices

### Controle de Acesso Inteligente
- Função `user_has_summary_access()` verifica permissões
- Controle temporal automático
- Seleção automática de resumos gratuitos

### Métricas e Analytics
- Tracking de progresso de leitura
- Estatísticas de uso no dashboard
- Logs de acesso para análise

### Segurança
- Autenticação JWT
- RLS em todas as tabelas
- Validação de dados em Edge Functions
- Proteção contra acesso não autorizado

## 🧪 Testes

```bash
npm run test
```

## 📚 Documentação da API

### Autenticação
Todas as rotas protegidas requerem header:
```
Authorization: Bearer <jwt_token>
```

### Códigos de Resposta
- `200` - Sucesso
- `401` - Não autenticado
- `403` - Acesso negado
- `404` - Não encontrado
- `500` - Erro interno

### Exemplo de Uso

```typescript
import { api } from './lib/api'

// Listar resumos
const summaries = await api.getSummaries({ page: 1, limit: 10 })

// Obter resumo específico
const summary = await api.getSummary('uuid')

// Atualizar progresso
await api.updateProgress('summary-id', 75)

// Criar checkout Stripe
const checkout = await api.createCheckoutSession('price_id')
```

## 🚀 Deploy

A API é automaticamente deployada via Supabase Edge Functions. Certifique-se de:

1. Configurar variáveis de ambiente no Supabase
2. Configurar webhooks do Stripe
3. Testar endpoints em produção

## 📝 Próximos Passos

- [ ] Implementar cache Redis
- [ ] Adicionar rate limiting
- [ ] Métricas avançadas com Analytics
- [ ] Sistema de recomendações
- [ ] API de busca com Elasticsearch
- [ ] Testes automatizados completos