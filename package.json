{"name": "book-summaries-platform", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@stripe/stripe-js": "^7.6.1", "@supabase/supabase-js": "^2.39.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cors": "^2.8.5", "express": "^5.1.0", "framer-motion": "^10.16.16", "fs-extra": "^11.3.0", "lucide-react": "^0.344.0", "pdf-extraction": "^1.0.2", "pdf-parse": "^1.1.1", "pdf-poppler": "^0.2.1", "pdf2json": "^3.1.6", "pdf2pic": "^3.2.0", "pdfjs-dist": "^5.3.93", "puppeteer": "^24.14.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.20.1", "stripe": "^18.3.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.0.0"}}