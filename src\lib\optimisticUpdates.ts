import { DashboardData } from './dataService'

export interface OptimisticUpdate {
  id: string
  type: 'favorite' | 'progress' | 'preferences' | 'subscription' | 'privacy'
  originalData: any
  optimisticData: any
  timestamp: number
  rollback: () => void
}

class OptimisticUpdateManager {
  private pendingUpdates: Map<string, OptimisticUpdate> = new Map()
  private updateCallbacks: Set<(updates: OptimisticUpdate[]) => void> = new Set()

  /**
   * Apply optimistic update
   */
  applyOptimisticUpdate(
    id: string,
    type: OptimisticUpdate['type'],
    originalData: any,
    optimisticData: any,
    rollback: () => void
  ): void {
    console.log(`⚡ Applying optimistic update: ${type}`, { id, optimisticData })

    const update: OptimisticUpdate = {
      id,
      type,
      originalData,
      optimisticData,
      timestamp: Date.now(),
      rollback
    }

    this.pendingUpdates.set(id, update)
    this.notifyCallbacks()

    // Auto-cleanup after 10 seconds if not resolved
    setTimeout(() => {
      if (this.pendingUpdates.has(id)) {
        console.warn(`⚠️ Optimistic update timeout: ${id}`)
        this.rollbackUpdate(id)
      }
    }, 10000)
  }

  /**
   * Confirm optimistic update (remove from pending)
   */
  confirmUpdate(id: string): void {
    if (this.pendingUpdates.has(id)) {
      console.log(`✅ Confirming optimistic update: ${id}`)
      this.pendingUpdates.delete(id)
      this.notifyCallbacks()
    }
  }

  /**
   * Rollback optimistic update
   */
  rollbackUpdate(id: string): void {
    const update = this.pendingUpdates.get(id)
    if (update) {
      console.log(`🔄 Rolling back optimistic update: ${id}`)
      try {
        update.rollback()
      } catch (error) {
        console.error(`❌ Error during rollback: ${id}`, error)
      }
      this.pendingUpdates.delete(id)
      this.notifyCallbacks()
    }
  }

  /**
   * Get all pending updates
   */
  getPendingUpdates(): OptimisticUpdate[] {
    return Array.from(this.pendingUpdates.values())
  }

  /**
   * Subscribe to update changes
   */
  subscribe(callback: (updates: OptimisticUpdate[]) => void): () => void {
    this.updateCallbacks.add(callback)
    return () => {
      this.updateCallbacks.delete(callback)
    }
  }

  private notifyCallbacks(): void {
    const updates = this.getPendingUpdates()
    this.updateCallbacks.forEach(callback => {
      try {
        callback(updates)
      } catch (error) {
        console.error('❌ Error in optimistic update callback:', error)
      }
    })
  }

  /**
   * Apply optimistic updates to dashboard data
   */
  applyToDashboardData(dashboardData: DashboardData): DashboardData {
    let modifiedData = { ...dashboardData }

    this.pendingUpdates.forEach((update) => {
      switch (update.type) {
        case 'favorite':
          modifiedData = this.applyFavoriteUpdate(modifiedData, update)
          break
        case 'progress':
          modifiedData = this.applyProgressUpdate(modifiedData, update)
          break
        case 'preferences':
          modifiedData = this.applyPreferencesUpdate(modifiedData, update)
          break
        case 'subscription':
          modifiedData = this.applySubscriptionUpdate(modifiedData, update)
          break
        case 'privacy':
          modifiedData = this.applyPrivacyUpdate(modifiedData, update)
          break
      }
    })

    return modifiedData
  }

  private applyFavoriteUpdate(data: DashboardData, update: OptimisticUpdate): DashboardData {
    const { bookId, isFavorited } = update.optimisticData

    // Update favorites list
    let favorites = [...data.favorites]
    const existingIndex = favorites.findIndex(f => f.book_id === bookId)

    if (isFavorited && existingIndex === -1) {
      // Add to favorites (we'll need book data from somewhere)
      favorites.push(update.optimisticData.favoriteRecord)
    } else if (!isFavorited && existingIndex !== -1) {
      // Remove from favorites
      favorites.splice(existingIndex, 1)
    }

    // Update stats
    const stats = {
      ...data.stats,
      favorites_count: favorites.length
    }

    return {
      ...data,
      favorites,
      stats
    }
  }

  private applyProgressUpdate(data: DashboardData, update: OptimisticUpdate): DashboardData {
    const { bookId, progressPercentage, isCompleted } = update.optimisticData

    // Update in_progress and completed lists
    let inProgress = [...data.in_progress]
    let completed = [...data.completed]

    // Remove from both lists first
    inProgress = inProgress.filter(p => p.book_id !== bookId)
    completed = completed.filter(p => p.book_id !== bookId)

    // Add to appropriate list
    if (isCompleted) {
      completed.push(update.optimisticData.progressRecord)
    } else if (progressPercentage > 0) {
      inProgress.push(update.optimisticData.progressRecord)
    }

    // Update stats
    const stats = {
      ...data.stats,
      in_progress_count: inProgress.length,
      total_summaries_read: completed.length
    }

    return {
      ...data,
      in_progress: inProgress,
      completed,
      stats
    }
  }

  private applyPreferencesUpdate(data: DashboardData, update: OptimisticUpdate): DashboardData {
    return {
      ...data,
      preferences: {
        ...data.preferences,
        ...update.optimisticData
      }
    }
  }

  private applySubscriptionUpdate(data: DashboardData, update: OptimisticUpdate): DashboardData {
    return {
      ...data,
      subscription: {
        ...data.subscription,
        ...update.optimisticData
      }
    }
  }

  private applyPrivacyUpdate(data: DashboardData, update: OptimisticUpdate): DashboardData {
    return {
      ...data,
      preferences: {
        ...data.preferences,
        privacy_settings: {
          ...data.preferences.privacy_settings,
          ...update.optimisticData
        }
      }
    }
  }

  /**
   * Clear all pending updates
   */
  clearAll(): void {
    console.log('🧹 Clearing all optimistic updates')
    this.pendingUpdates.clear()
    this.notifyCallbacks()
  }
}

// Export singleton instance
export const optimisticUpdateManager = new OptimisticUpdateManager()
