# PARETTO - <PERSON><PERSON><PERSON><PERSON><PERSON> EDGE FUNCTIONS DEPLOYMENT
# ============================================

Write-Host "🚀 PARETTO - SUPABASE EDGE FUNCTIONS DEPLOYMENT" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan

# Configuration
$PROJECT_REF = "qmeelujsnpbcdkzhcwmm"
$STRIPE_SECRET_KEY = "sk_test_51RIcDWQXmlk54RXco22WsOUYY2jtE27RhFyvbaaiInFpIDD5yFcbQNPjSilbv6pzKxq1YuihBgAy4ria1OV3l2mY00gw9ED5fy"
$STRIPE_WEBHOOK_SECRET = "whsec_rSOSlhBwwbkRSVfgqla96EctF9NTcL9c"
$SUPABASE_URL = "https://qmeelujsnpbcdkzhcwmm.supabase.co"
$SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4"

function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Warning {
    param($Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Info {
    param($Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

# Check if Supabase CLI is installed
function Check-SupabaseCLI {
    Write-Info "Checking Supabase CLI installation..."
    
    try {
        $version = supabase --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Supabase CLI found: $version"
            return $true
        }
    }
    catch {
        # CLI not found
    }
    
    Write-Error "Supabase CLI not found!"
    Write-Host ""
    Write-Host "Please install Supabase CLI:" -ForegroundColor Yellow
    Write-Host "  npm install -g supabase" -ForegroundColor White
    Write-Host "  or visit: https://supabase.com/docs/guides/cli" -ForegroundColor White
    return $false
}

# Login to Supabase
function Login-Supabase {
    Write-Info "Checking Supabase authentication..."
    
    try {
        supabase projects list 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Already logged in to Supabase"
            return $true
        }
    }
    catch {
        # Not logged in
    }
    
    Write-Info "Please login to Supabase..."
    supabase login
    return $LASTEXITCODE -eq 0
}

# Initialize and link project
function Setup-Project {
    Write-Info "Setting up Supabase project..."
    
    # Initialize if not already done
    if (!(Test-Path "supabase")) {
        Write-Info "Initializing Supabase project..."
        supabase init
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to initialize Supabase project"
            return $false
        }
    } else {
        Write-Success "Supabase project already initialized"
    }
    
    # Link project
    Write-Info "Linking to Supabase project..."
    supabase link --project-ref $PROJECT_REF
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Project linked successfully"
        return $true
    } else {
        Write-Error "Failed to link project"
        return $false
    }
}

# Set secrets
function Set-Secrets {
    Write-Info "Setting up Supabase secrets..."
    
    Write-Host "Setting STRIPE_SECRET_KEY..." -ForegroundColor Gray
    supabase secrets set "STRIPE_SECRET_KEY=$STRIPE_SECRET_KEY"
    
    Write-Host "Setting STRIPE_WEBHOOK_SECRET..." -ForegroundColor Gray
    supabase secrets set "STRIPE_WEBHOOK_SECRET=$STRIPE_WEBHOOK_SECRET"
    
    Write-Host "Setting SUPABASE_URL..." -ForegroundColor Gray
    supabase secrets set "SUPABASE_URL=$SUPABASE_URL"
    
    Write-Host "Setting SUPABASE_ANON_KEY..." -ForegroundColor Gray
    supabase secrets set "SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY"
    
    Write-Success "Secrets configured"
    
    # List secrets to verify
    Write-Info "Verifying secrets..."
    supabase secrets list
}

# Deploy functions
function Deploy-Functions {
    Write-Info "Deploying Edge Functions..."
    
    # Deploy stripe-checkout
    Write-Host ""
    Write-Info "Deploying stripe-checkout function..."
    supabase functions deploy stripe-checkout
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "stripe-checkout deployed successfully"
    } else {
        Write-Error "Failed to deploy stripe-checkout"
        return $false
    }
    
    # Deploy stripe-webhook
    Write-Host ""
    Write-Info "Deploying stripe-webhook function..."
    supabase functions deploy stripe-webhook
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "stripe-webhook deployed successfully"
    } else {
        Write-Error "Failed to deploy stripe-webhook"
        return $false
    }
    
    # Deploy stripe-portal
    Write-Host ""
    Write-Info "Deploying stripe-portal function..."
    supabase functions deploy stripe-portal
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "stripe-portal deployed successfully"
        return $true
    } else {
        Write-Error "Failed to deploy stripe-portal"
        return $false
    }
}

# Show deployment info
function Show-DeploymentInfo {
    Write-Host ""
    Write-Success "🎉 All Edge Functions deployed successfully!"
    Write-Host ""
    Write-Info "📋 Function URLs:"
    Write-Host "   Checkout: $SUPABASE_URL/functions/v1/stripe-checkout" -ForegroundColor White
    Write-Host "   Webhook:  $SUPABASE_URL/functions/v1/stripe-webhook" -ForegroundColor White
    Write-Host "   Portal:   $SUPABASE_URL/functions/v1/stripe-portal" -ForegroundColor White
    Write-Host ""
    Write-Info "📊 To view function logs:"
    Write-Host "   supabase functions logs stripe-checkout" -ForegroundColor White
    Write-Host "   supabase functions logs stripe-webhook" -ForegroundColor White
    Write-Host "   supabase functions logs stripe-portal" -ForegroundColor White
    Write-Host ""
    Write-Info "🔧 Next steps:"
    Write-Host "   1. Update your .env file:" -ForegroundColor White
    Write-Host "      VITE_EDGE_FUNCTIONS_URL=$SUPABASE_URL/functions/v1" -ForegroundColor White
    Write-Host "   2. Test the integration in your application" -ForegroundColor White
    Write-Host "   3. Monitor function logs for any issues" -ForegroundColor White
    Write-Host ""
    Write-Info "🎯 Stripe Webhook URL (already configured):"
    Write-Host "   $SUPABASE_URL/functions/v1/stripe-webhook" -ForegroundColor White
}

# Main execution
function Main {
    if (!(Check-SupabaseCLI)) {
        exit 1
    }
    
    if (!(Login-Supabase)) {
        Write-Error "Failed to login to Supabase"
        exit 1
    }
    
    if (!(Setup-Project)) {
        Write-Error "Failed to setup project"
        exit 1
    }
    
    Set-Secrets
    
    if (!(Deploy-Functions)) {
        Write-Error "Failed to deploy functions"
        exit 1
    }
    
    Show-DeploymentInfo
}

# Run main function
Main
