const { execSync } = require('child_process');

console.log('🔐 CONFIGURANDO VARIÁVEIS DE AMBIENTE NO SUPABASE (via npx)');
console.log('=========================================================\n');

// Configurações
const secrets = {
  'STRIPE_SECRET_KEY': 'sk_test_51RIcDWQXmlk54RXco22WsOUYY2jtE27RhFyvbaaiInFpIDD5yFcbQNPjSilbv6pzKxq1YuihBgAy4ria1OV3l2mY00gw9ED5fy',
  'STRIPE_WEBHOOK_SECRET': 'whsec_rSOSlhBwwbkRSVfgqla96EctF9NTcL9c',
  'SUPABASE_URL': 'https://qmeelujsnpbcdkzhcwmm.supabase.co',
  'SUPABASE_ANON_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4',
  'SUPABASE_SERVICE_ROLE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjM2MjM0NSwiZXhwIjoyMDYxOTM4MzQ1fQ.zDusZDEOpQjEjgZhydao_ShbqddElyIA-rExxBj8yCM'
};

function runCommand(command, description) {
  console.log(`🔄 ${description}...`);
  try {
    const output = execSync(command, { encoding: 'utf8', stdio: 'inherit' });
    console.log(`✅ ${description} - Sucesso`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} - Erro:`, error.message);
    return false;
  }
}

function checkSupabaseCLI() {
  console.log('🔍 Verificando Supabase CLI via npx...');
  try {
    const version = execSync('npx supabase --version', { encoding: 'utf8' });
    console.log(`✅ Supabase CLI encontrado: ${version.trim()}`);
    return true;
  } catch (error) {
    console.log('❌ Erro ao executar Supabase CLI via npx:', error.message);
    return false;
  }
}

function loginSupabase() {
  console.log('\n🔑 Fazendo login no Supabase...');
  console.log('⚠️  Uma janela do navegador será aberta para login');
  
  try {
    execSync('npx supabase login', { stdio: 'inherit' });
    console.log('✅ Login realizado com sucesso');
    return true;
  } catch (error) {
    console.log('❌ Erro no login:', error.message);
    return false;
  }
}

function linkProject() {
  console.log('\n🔗 Linkando projeto...');
  return runCommand('npx supabase link --project-ref qmeelujsnpbcdkzhcwmm', 'Link do projeto');
}

function setSecrets() {
  console.log('\n🔐 Configurando secrets...');
  
  let successCount = 0;
  let totalSecrets = Object.keys(secrets).length;
  
  for (const [key, value] of Object.entries(secrets)) {
    console.log(`\n📝 Configurando ${key}...`);
    
    // Escapar aspas no valor
    const escapedValue = value.replace(/"/g, '\\"');
    const command = `npx supabase secrets set ${key}="${escapedValue}"`;
    
    if (runCommand(command, `Set ${key}`)) {
      successCount++;
    }
  }
  
  console.log(`\n📊 Resultado: ${successCount}/${totalSecrets} secrets configurados`);

  if (successCount === totalSecrets) {
    console.log('\n✅ TODOS OS SECRETS CONFIGURADOS COM SUCESSO!');
    console.log('   - STRIPE_SECRET_KEY ✅');
    console.log('   - STRIPE_WEBHOOK_SECRET ✅');
    console.log('   - SUPABASE_URL ✅');
    console.log('   - SUPABASE_ANON_KEY ✅');
    console.log('   - SUPABASE_SERVICE_ROLE_KEY ✅');
  }

  return successCount === totalSecrets;
}

function listSecrets() {
  console.log('\n📋 Verificando secrets configurados...');
  try {
    execSync('npx supabase secrets list', { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error('❌ Erro ao listar secrets:', error.message);
    return false;
  }
}

function deployFunctions() {
  console.log('\n🚀 Fazendo deploy das Edge Functions...');
  
  const functions = ['stripe-checkout', 'stripe-webhook', 'stripe-portal'];
  let successCount = 0;
  
  for (const func of functions) {
    console.log(`\n📦 Deploy da função ${func}...`);
    if (runCommand(`npx supabase functions deploy ${func}`, `Deploy ${func}`)) {
      successCount++;
    }
  }
  
  console.log(`\n📊 Deploy resultado: ${successCount}/${functions.length} functions deployadas`);
  return successCount === functions.length;
}

function showFinalInstructions() {
  console.log('\n🎉 CONFIGURAÇÃO CONCLUÍDA!');
  console.log('==========================');
  console.log('');
  console.log('🔗 URLs das Functions:');
  console.log('   Checkout: https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-checkout');
  console.log('   Webhook:  https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-webhook');
  console.log('   Portal:   https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal');
  console.log('');
  console.log('📊 Para monitorar:');
  console.log('   npx supabase functions logs stripe-webhook');
  console.log('');
  console.log('🧪 Próximos passos:');
  console.log('   1. Configure a Service Role Key (instruções acima)');
  console.log('   2. Teste a integração na sua aplicação');
  console.log('   3. Monitore os logs das functions');
  console.log('');
  console.log('🎯 Webhook do Stripe já configurado em:');
  console.log('   https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-webhook');
}

// Executar configuração
async function main() {
  // Verificar CLI
  if (!checkSupabaseCLI()) {
    console.log('\n💡 Tentando instalar Supabase CLI...');
    console.log('Se falhar, visite: https://supabase.com/docs/guides/cli');
    process.exit(1);
  }
  
  // Login
  if (!loginSupabase()) {
    console.log('❌ Falha no login. Tente novamente.');
    process.exit(1);
  }
  
  // Link do projeto
  if (!linkProject()) {
    console.log('❌ Falha ao linkar projeto');
    process.exit(1);
  }
  
  // Configurar secrets
  setSecrets();
  
  // Listar secrets
  listSecrets();
  
  // Deploy functions
  deployFunctions();
  
  // Instruções finais
  showFinalInstructions();
}

main().catch(error => {
  console.error('❌ Erro na configuração:', error.message);
  process.exit(1);
});
