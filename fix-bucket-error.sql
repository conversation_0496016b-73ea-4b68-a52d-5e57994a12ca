-- Corrigir erro de bucket "Bucket not found" no Supabase Storage
-- Execute este SQL no Supabase Dashboard > SQL Editor

-- STEP 1: Verificar buckets existentes
SELECT 'BUCKETS EXISTENTES' as step;

SELECT 
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets
ORDER BY name;

-- STEP 2: Criar bucket 'books' se não existir
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'books') THEN
        INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
        VALUES (
            'books',
            'books',
            true,
            52428800, -- 50MB limit
            ARRAY['application/pdf']
        );
        RAISE NOTICE 'Bucket "books" criado com sucesso';
    ELSE
        RAISE NOTICE 'Bucket "books" já existe';
    END IF;
END $$;

-- STEP 3: Verificar livros que estão causando o erro
SELECT 'LIVROS COM PROBLEMA DE BUCKET' as step;

SELECT 
    id,
    title,
    author,
    pdf_key,
    CASE 
        WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 'TEM PDF_KEY'
        ELSE 'SEM PDF_KEY'
    END as pdf_status
FROM books 
WHERE LOWER(title) LIKE '%anuário%fenomenologia%'
   OR LOWER(title) LIKE '%studium%'
   OR LOWER(title) LIKE '%fenomenologia%';

-- STEP 4: Corrigir livros problemáticos - remover pdf_key inválido
UPDATE books 
SET pdf_key = NULL
WHERE (LOWER(title) LIKE '%anuário%fenomenologia%'
       OR LOWER(title) LIKE '%studium%'
       OR LOWER(title) LIKE '%fenomenologia%')
  AND pdf_key IS NOT NULL
  AND pdf_key != '';

-- STEP 5: Verificar se há conteúdo estruturado para esses livros
SELECT 'CONTEÚDO ESTRUTURADO DISPONÍVEL' as step;

SELECT 
    b.id,
    b.title,
    b.author,
    CASE 
        WHEN bc.content IS NOT NULL THEN 'TEM CONTEÚDO ESTRUTURADO'
        ELSE 'SEM CONTEÚDO'
    END as content_status,
    CASE 
        WHEN bc.content IS NOT NULL THEN LENGTH(bc.content::text)
        ELSE 0
    END as content_length
FROM books b
LEFT JOIN book_contents bc ON bc.book_id = b.id
WHERE LOWER(b.title) LIKE '%anuário%fenomenologia%'
   OR LOWER(b.title) LIKE '%studium%'
   OR LOWER(b.title) LIKE '%fenomenologia%';

-- STEP 6: Criar políticas RLS para o bucket 'books'
-- Permitir leitura pública dos PDFs
DO $$
BEGIN
    -- Verificar se a política já existe
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'storage' 
        AND tablename = 'objects' 
        AND policyname = 'Public Books Access'
    ) THEN
        -- Criar política de leitura pública
        EXECUTE 'CREATE POLICY "Public Books Access" ON storage.objects
                 FOR SELECT USING (bucket_id = ''books'')';
        RAISE NOTICE 'Política "Public Books Access" criada';
    ELSE
        RAISE NOTICE 'Política "Public Books Access" já existe';
    END IF;
    
    -- Política para upload (se necessário)
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'storage' 
        AND tablename = 'objects' 
        AND policyname = 'Allow Books Upload'
    ) THEN
        EXECUTE 'CREATE POLICY "Allow Books Upload" ON storage.objects
                 FOR INSERT WITH CHECK (bucket_id = ''books'')';
        RAISE NOTICE 'Política "Allow Books Upload" criada';
    ELSE
        RAISE NOTICE 'Política "Allow Books Upload" já existe';
    END IF;
END $$;

-- STEP 7: Verificar se há PDFs órfãos na tabela pdf_files
SELECT 'VERIFICANDO PDFs NA TABELA pdf_files' as step;

SELECT 
    pf.id,
    pf.book_id,
    pf.filename,
    b.title,
    CASE 
        WHEN b.id IS NOT NULL THEN 'LIVRO EXISTE'
        ELSE 'LIVRO NÃO EXISTE'
    END as book_status
FROM pdf_files pf
LEFT JOIN books b ON b.id::text = pf.book_id
WHERE pf.filename LIKE '%fenomenologia%'
   OR pf.filename LIKE '%studium%'
   OR pf.book_id IN (
       SELECT id::text FROM books 
       WHERE LOWER(title) LIKE '%anuário%fenomenologia%'
          OR LOWER(title) LIKE '%studium%'
   );

-- STEP 8: Limpar dados órfãos se necessário
DELETE FROM pdf_files 
WHERE book_id NOT IN (SELECT id::text FROM books);

-- STEP 9: Verificação final
SELECT 'VERIFICAÇÃO FINAL' as step;

-- Verificar buckets
SELECT 
    'Bucket books existe' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'books') THEN '✅ SIM'
        ELSE '❌ NÃO'
    END as status

UNION ALL

-- Verificar políticas
SELECT 
    'Políticas RLS criadas' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE schemaname = 'storage' 
            AND tablename = 'objects' 
            AND policyname = 'Public Books Access'
        ) THEN '✅ SIM'
        ELSE '❌ NÃO'
    END as status

UNION ALL

-- Verificar livros problemáticos
SELECT 
    'Livros com pdf_key inválido' as check_type,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ NENHUM'
        ELSE '⚠️ ' || COUNT(*)::text || ' ENCONTRADOS'
    END as status
FROM books 
WHERE (LOWER(title) LIKE '%anuário%fenomenologia%'
       OR LOWER(title) LIKE '%studium%'
       OR LOWER(title) LIKE '%fenomenologia%')
  AND pdf_key IS NOT NULL
  AND pdf_key != '';

-- STEP 10: Mostrar status dos livros de fenomenologia
SELECT 'STATUS DOS LIVROS DE FENOMENOLOGIA' as step;

SELECT 
    b.id,
    b.title,
    b.author,
    CASE 
        WHEN b.pdf_key IS NOT NULL AND b.pdf_key != '' THEN '📄 PDF_KEY: ' || b.pdf_key
        WHEN bc.content IS NOT NULL THEN '📝 CONTEÚDO ESTRUTURADO'
        ELSE '❌ SEM CONTEÚDO'
    END as content_type,
    CASE 
        WHEN b.pdf_key IS NULL AND bc.content IS NOT NULL THEN '✅ FUNCIONARÁ'
        WHEN b.pdf_key IS NULL AND bc.content IS NULL THEN '❌ PRECISA CONTEÚDO'
        ELSE '⚠️ VERIFICAR'
    END as status
FROM books b
LEFT JOIN book_contents bc ON bc.book_id = b.id
WHERE LOWER(b.title) LIKE '%anuário%fenomenologia%'
   OR LOWER(b.title) LIKE '%studium%'
   OR LOWER(b.title) LIKE '%fenomenologia%'
ORDER BY b.title;

-- Mensagem final
SELECT 
    '✅ CORREÇÃO DE BUCKET CONCLUÍDA' as status,
    'Bucket "books" criado e políticas configuradas' as bucket_status,
    'PDFs inválidos removidos dos livros problemáticos' as cleanup_status;
