/*
  # Complete User Features Implementation
  
  This migration adds all missing tables and features for:
  1. Reading streak tracking
  2. User preferences and privacy settings
  3. Trending books analytics
  4. Enhanced user progress tracking
  5. Personalized recommendations data
*/

-- Create user_reading_streak table
CREATE TABLE IF NOT EXISTS user_reading_streak (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  current_streak integer DEFAULT 0 NOT NULL,
  longest_streak integer DEFAULT 0 NOT NULL,
  last_read_date date,
  this_week_count integer DEFAULT 0 NOT NULL,
  week_start_date date DEFAULT CURRENT_DATE NOT NULL,
  total_reading_days integer DEFAULT 0 NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  UNIQUE(user_id)
);

-- Create user_preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  preferred_categories text[] DEFAULT '{}',
  reading_goal_minutes_per_day integer DEFAULT 30,
  notification_settings jsonb DEFAULT '{
    "daily_reminder": true,
    "streak_milestone": true,
    "new_recommendations": true,
    "promotional_emails": false
  }',
  privacy_settings jsonb DEFAULT '{
    "share_reading_progress": true,
    "allow_analytics": true,
    "public_profile": false
  }',
  theme_preference text DEFAULT 'light' CHECK (theme_preference IN ('light', 'dark', 'auto')),
  language_preference text DEFAULT 'pt-BR',
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  UNIQUE(user_id)
);

-- Create book_analytics table for trending calculations
CREATE TABLE IF NOT EXISTS book_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  book_id integer NOT NULL REFERENCES books(id) ON DELETE CASCADE,
  date date DEFAULT CURRENT_DATE NOT NULL,
  views_count integer DEFAULT 0,
  reading_sessions integer DEFAULT 0,
  completion_count integer DEFAULT 0,
  favorite_count integer DEFAULT 0,
  total_reading_time_minutes integer DEFAULT 0,
  unique_readers integer DEFAULT 0,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  UNIQUE(book_id, date)
);

-- Create user_recommendations table
CREATE TABLE IF NOT EXISTS user_recommendations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  book_id integer NOT NULL REFERENCES books(id) ON DELETE CASCADE,
  recommendation_score numeric(3,2) DEFAULT 0.0 NOT NULL,
  recommendation_reason text,
  based_on_books integer[] DEFAULT '{}',
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  UNIQUE(user_id, book_id)
);

-- Update user_reading_progress table to include book_id reference and page tracking
ALTER TABLE user_reading_progress
ADD COLUMN IF NOT EXISTS book_id integer REFERENCES books(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS current_page integer DEFAULT 1 NOT NULL,
ADD COLUMN IF NOT EXISTS total_pages integer DEFAULT 1 NOT NULL,
ADD COLUMN IF NOT EXISTS last_page_read integer DEFAULT 1 NOT NULL,
ADD COLUMN IF NOT EXISTS reading_session_count integer DEFAULT 0 NOT NULL,
ADD COLUMN IF NOT EXISTS total_reading_time_seconds integer DEFAULT 0 NOT NULL;

-- Update existing records to set book_id from summary_id
UPDATE user_reading_progress
SET book_id = summary_id::integer
WHERE book_id IS NULL AND summary_id ~ '^[0-9]+$';

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_reading_streak_user_id ON user_reading_streak(user_id);
CREATE INDEX IF NOT EXISTS idx_user_reading_streak_current_streak ON user_reading_streak(current_streak DESC);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_book_analytics_book_id ON book_analytics(book_id);
CREATE INDEX IF NOT EXISTS idx_book_analytics_date ON book_analytics(date DESC);
CREATE INDEX IF NOT EXISTS idx_book_analytics_trending ON book_analytics(date DESC, views_count DESC, reading_sessions DESC);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_user_id ON user_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_recommendations_score ON user_recommendations(user_id, recommendation_score DESC);

-- Enable RLS on new tables
ALTER TABLE user_reading_streak ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_recommendations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_reading_streak
CREATE POLICY "Users can view their own reading streak" ON user_reading_streak
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own reading streak" ON user_reading_streak
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reading streak" ON user_reading_streak
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for user_preferences
CREATE POLICY "Users can view their own preferences" ON user_preferences
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own preferences" ON user_preferences
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON user_preferences
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for book_analytics (read-only for users, admin can write)
CREATE POLICY "Anyone can view book analytics" ON book_analytics
  FOR SELECT USING (true);

-- RLS Policies for user_recommendations
CREATE POLICY "Users can view their own recommendations" ON user_recommendations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage recommendations" ON user_recommendations
  FOR ALL USING (true);

-- Create function to update reading streak
CREATE OR REPLACE FUNCTION update_reading_streak(user_id_param uuid)
RETURNS void AS $$
DECLARE
  last_read date;
  current_streak_val integer;
  longest_streak_val integer;
  today date := CURRENT_DATE;
  yesterday date := CURRENT_DATE - INTERVAL '1 day';
BEGIN
  -- Get current streak data
  SELECT last_read_date, current_streak, longest_streak
  INTO last_read, current_streak_val, longest_streak_val
  FROM user_reading_streak
  WHERE user_id = user_id_param;
  
  -- If no streak record exists, create one
  IF NOT FOUND THEN
    INSERT INTO user_reading_streak (user_id, current_streak, longest_streak, last_read_date, total_reading_days)
    VALUES (user_id_param, 1, 1, today, 1);
    RETURN;
  END IF;
  
  -- Don't update if already read today
  IF last_read = today THEN
    RETURN;
  END IF;
  
  -- Update streak logic
  IF last_read = yesterday THEN
    -- Continue streak
    current_streak_val := current_streak_val + 1;
  ELSE
    -- Reset streak
    current_streak_val := 1;
  END IF;
  
  -- Update longest streak if needed
  IF current_streak_val > longest_streak_val THEN
    longest_streak_val := current_streak_val;
  END IF;
  
  -- Update the record
  UPDATE user_reading_streak
  SET 
    current_streak = current_streak_val,
    longest_streak = longest_streak_val,
    last_read_date = today,
    total_reading_days = total_reading_days + 1,
    updated_at = now()
  WHERE user_id = user_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update book analytics
CREATE OR REPLACE FUNCTION update_book_analytics(book_id_param integer, event_type text)
RETURNS void AS $$
BEGIN
  INSERT INTO book_analytics (book_id, date, views_count, reading_sessions, completion_count, favorite_count)
  VALUES (book_id_param, CURRENT_DATE, 
    CASE WHEN event_type = 'view' THEN 1 ELSE 0 END,
    CASE WHEN event_type = 'reading_session' THEN 1 ELSE 0 END,
    CASE WHEN event_type = 'completion' THEN 1 ELSE 0 END,
    CASE WHEN event_type = 'favorite' THEN 1 ELSE 0 END
  )
  ON CONFLICT (book_id, date) DO UPDATE SET
    views_count = book_analytics.views_count + CASE WHEN event_type = 'view' THEN 1 ELSE 0 END,
    reading_sessions = book_analytics.reading_sessions + CASE WHEN event_type = 'reading_session' THEN 1 ELSE 0 END,
    completion_count = book_analytics.completion_count + CASE WHEN event_type = 'completion' THEN 1 ELSE 0 END,
    favorite_count = book_analytics.favorite_count + CASE WHEN event_type = 'favorite' THEN 1 ELSE 0 END,
    updated_at = now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers to automatically update updated_at timestamps
CREATE TRIGGER update_user_reading_streak_updated_at
  BEFORE UPDATE ON user_reading_streak
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at
  BEFORE UPDATE ON user_preferences
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_book_analytics_updated_at
  BEFORE UPDATE ON book_analytics
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_recommendations_updated_at
  BEFORE UPDATE ON user_recommendations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
