# 🔑 COMO OBTER A SERVICE ROLE KEY

## 📋 **Passo a Passo:**

### **1. Acesse o Dashboard do Supabase:**
```
https://supabase.com/dashboard/project/qmeelujsnpbcdkzhcwmm
```

### **2. Navegue para Settings → API:**
- No menu lateral, clique em **"Settings"**
- Clique em **"API"**

### **3. Copie a Service Role Key:**
- Procure por **"service_role"** na seção **"Project API keys"**
- Clique no ícone de **"olho"** para revelar a chave
- **Copie a chave completa**

### **4. A chave deve começar com:**
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6InNlcnZpY2Vfcm9sZSI...
```

## 🔧 **Alternativa - Comandos Manuais:**

Se você já tem a Service Role Key, execute estes comandos:

```bash
# 1. Login no Supabase
supabase login

# 2. Link do projeto
supabase link --project-ref qmeelujsnpbcdkzhcwmm

# 3. Configurar secrets (substitua SUA_SERVICE_ROLE_KEY pela chave real)
supabase secrets set STRIPE_SECRET_KEY="sk_test_51RIcDWQXmlk54RXco22WsOUYY2jtE27RhFyvbaaiInFpIDD5yFcbQNPjSilbv6pzKxq1YuihBgAy4ria1OV3l2mY00gw9ED5fy"

supabase secrets set STRIPE_WEBHOOK_SECRET="whsec_rSOSlhBwwbkRSVfgqla96EctF9NTcL9c"

supabase secrets set SUPABASE_URL="https://qmeelujsnpbcdkzhcwmm.supabase.co"

supabase secrets set SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4"

supabase secrets set SUPABASE_SERVICE_ROLE_KEY="SUA_SERVICE_ROLE_KEY_AQUI"

# 4. Verificar secrets
supabase secrets list

# 5. Deploy das functions
supabase functions deploy stripe-checkout
supabase functions deploy stripe-webhook  
supabase functions deploy stripe-portal
```

## ⚡ **Script Automático:**

Depois de obter a Service Role Key, atualize o arquivo `configure-supabase-secrets.cjs` na linha:

```javascript
'SUPABASE_SERVICE_ROLE_KEY': 'SUA_CHAVE_AQUI'
```

E execute:
```bash
node configure-supabase-secrets.cjs
```
