import { loadStripe, Stripe } from '@stripe/stripe-js'

// Stripe publishable key - should be added to .env
const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_51234567890abcdef'

// Initialize Stripe
let stripePromise: Promise<Stripe | null>

export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(stripePublishableKey)
  }
  return stripePromise
}

// Subscription plans configuration
export const SUBSCRIPTION_PLANS = {
  FREE: {
    id: 'free',
    name: 'Gratuito',
    price: 0,
    currency: 'BRL',
    interval: 'month',
    features: [
      '5 resumos gratuitos por mês',
      'Acesso básico à biblioteca',
      'Progresso de leitura',
      'Favoritos'
    ],
    limits: {
      monthly_summaries: 5,
      offline_access: false,
      premium_content: false,
      priority_support: false
    }
  },
  PREMIUM: {
    id: 'premium',
    name: 'Premium',
    price: 1990, // R$ 19.90 in cents
    currency: 'BRL',
    interval: 'month',
    stripe_price_id: 'price_1Rov58QXmlk54RXcFlW7ymIL',
    features: [
      'Resumos ilimitados',
      'Acesso completo à biblioteca',
      'Conteúdo premium exclusivo',
      'Download para leitura offline',
      'Suporte prioritário',
      'Estatísticas avançadas'
    ],
    limits: {
      monthly_summaries: -1, // unlimited
      offline_access: true,
      premium_content: true,
      priority_support: true
    }
  },
  ANNUAL: {
    id: 'annual',
    name: 'Anual',
    price: 19900, // R$ 199.00 in cents (save ~17%)
    currency: 'BRL',
    interval: 'year',
    stripe_price_id: 'price_1Rov5AQXmlk54RXcfMEsaSA3',
    features: [
      'Todos os benefícios Premium',
      'Economia de 17% no plano anual',
      'Acesso antecipado a novos recursos',
      'Consultoria personalizada'
    ],
    limits: {
      monthly_summaries: -1,
      offline_access: true,
      premium_content: true,
      priority_support: true,
      early_access: true
    }
  }
}

export type SubscriptionPlan = keyof typeof SUBSCRIPTION_PLANS
export type PlanDetails = typeof SUBSCRIPTION_PLANS[SubscriptionPlan]

// Stripe service class
export class StripeService {
  private stripe: Stripe | null = null

  async initialize(): Promise<void> {
    if (!this.stripe) {
      this.stripe = await getStripe()
      if (!this.stripe) {
        throw new Error('Failed to initialize Stripe')
      }
    }
  }

  /**
   * Create a checkout session for subscription
   */
  async createCheckoutSession(
    planId: SubscriptionPlan,
    userId: string,
    successUrl: string,
    cancelUrl: string
  ): Promise<{ sessionId: string }> {
    const plan = SUBSCRIPTION_PLANS[planId]
    
    if (!plan.stripe_price_id) {
      throw new Error(`Plan ${planId} does not support Stripe checkout`)
    }

    try {
      // For development, simulate checkout session creation
      // Remove this block when Edge Functions are deployed
      if (import.meta.env.DEV && !import.meta.env.VITE_USE_PRODUCTION_STRIPE) {
        console.log('Development mode: Simulating Stripe checkout session creation')
        console.log('Plan:', plan)
        console.log('User ID:', userId)

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Return mock session ID
        return { sessionId: `cs_test_${Date.now()}_${planId}` }
      }

      // Use Supabase Edge Function in production
      const edgeFunctionsUrl = import.meta.env.VITE_EDGE_FUNCTIONS_URL || import.meta.env.VITE_SUPABASE_URL + '/functions/v1'
      const response = await fetch(`${edgeFunctionsUrl}/stripe-checkout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        },
        body: JSON.stringify({
          price_id: plan.stripe_price_id,
          user_id: userId,
          success_url: successUrl,
          cancel_url: cancelUrl,
          plan_id: planId
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create checkout session')
      }

      const { session_id } = await response.json()
      return { sessionId: session_id }
    } catch (error) {
      console.error('Error creating checkout session:', error)
      throw error
    }
  }

  /**
   * Redirect to Stripe Checkout
   */
  async redirectToCheckout(sessionId: string): Promise<void> {
    // In development mode, simulate successful checkout
    // Remove this block when Edge Functions are deployed
    if (import.meta.env.DEV && !import.meta.env.VITE_USE_PRODUCTION_STRIPE) {
      console.log('Development mode: Simulating Stripe checkout redirect')
      console.log('Session ID:', sessionId)

      // Extract plan from session ID
      const planId = sessionId.split('_').pop()

      // Simulate successful checkout by redirecting to success page
      const successUrl = `${window.location.origin}/subscription/success?session_id=${sessionId}&plan=${planId}`
      window.location.href = successUrl
      return
    }

    await this.initialize()

    if (!this.stripe) {
      throw new Error('Stripe not initialized')
    }

    const { error } = await this.stripe.redirectToCheckout({
      sessionId
    })

    if (error) {
      console.error('Stripe checkout error:', error)
      throw error
    }
  }

  /**
   * Create a customer portal session
   */
  async createPortalSession(customerId: string, returnUrl: string): Promise<{ url: string }> {
    try {
      const edgeFunctionsUrl = import.meta.env.VITE_EDGE_FUNCTIONS_URL || import.meta.env.VITE_SUPABASE_URL + '/functions/v1'
      const response = await fetch(`${edgeFunctionsUrl}/stripe-portal`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        },
        body: JSON.stringify({
          customer_id: customerId,
          return_url: returnUrl
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create portal session')
      }

      const { url } = await response.json()
      return { url }
    } catch (error) {
      console.error('Error creating portal session:', error)
      throw error
    }
  }

  /**
   * Get subscription status
   */
  async getSubscriptionStatus(userId: string): Promise<{
    status: string
    current_period_end: string
    cancel_at_period_end: boolean
    plan: SubscriptionPlan
  }> {
    try {
      const response = await fetch(`/api/stripe/subscription-status?user_id=${userId}`)
      
      if (!response.ok) {
        throw new Error('Failed to get subscription status')
      }

      return await response.json()
    } catch (error) {
      console.error('Error getting subscription status:', error)
      throw error
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(subscriptionId: string): Promise<void> {
    try {
      const response = await fetch('/api/stripe/cancel-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription_id: subscriptionId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to cancel subscription')
      }
    } catch (error) {
      console.error('Error canceling subscription:', error)
      throw error
    }
  }
}

// Export singleton instance
export const stripeService = new StripeService()

// Utility functions
export function formatPrice(amount: number, currency: string = 'BRL'): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: currency,
  }).format(amount / 100)
}

export function getPlanByStripeId(stripePriceId: string): PlanDetails | null {
  for (const plan of Object.values(SUBSCRIPTION_PLANS)) {
    if (plan.stripe_price_id === stripePriceId) {
      return plan
    }
  }
  return null
}
