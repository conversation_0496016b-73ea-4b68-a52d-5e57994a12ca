-- Fix "Hábitos Atômicos" Book - Remove Duplicates and Ensure PDF Loading
-- Execute this SQL in Supabase Dashboard > SQL Editor

-- STEP 1: Identify all "Hábitos Atômicos" versions
SELECT 'CURRENT HÁBITOS ATÔMICOS VERSIONS' as step;

SELECT 
    id,
    title,
    author,
    category,
    duration,
    difficulty,
    pdf_key,
    is_featured,
    created_at,
    CASE 
        WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN '✅ HAS PDF_KEY'
        ELSE '❌ NO PDF_KEY'
    END as pdf_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM book_contents bc WHERE bc.book_id = books.id) THEN '📄 HAS CONTENT'
        ELSE '❌ NO CONTENT'
    END as content_status
FROM books 
WHERE LOWER(title) LIKE '%hábitos atômicos%' 
   OR LOWER(title) LIKE '%habitos atomicos%'
ORDER BY created_at DESC;

-- STEP 2: Check for placeholder content in book_contents
SELECT 'CHECKING FOR PLACEHOLDER CONTENT' as step;

SELECT 
    bc.id,
    b.title,
    CASE 
        WHEN bc.content::text LIKE '%conteúdo completo está sendo processado%' THEN '❌ PLACEHOLDER'
        WHEN bc.content::text LIKE '%Nossa equipe está trabalhando%' THEN '❌ PLACEHOLDER'
        WHEN LENGTH(bc.content::text) < 1000 THEN '⚠️ SHORT CONTENT'
        ELSE '✅ REAL CONTENT'
    END as content_type,
    LENGTH(bc.content::text) as content_length
FROM book_contents bc
JOIN books b ON b.id = bc.book_id
WHERE LOWER(b.title) LIKE '%hábitos atômicos%' 
   OR LOWER(b.title) LIKE '%habitos atomicos%';

-- STEP 3: Identify the best version to keep
-- Priority: 1) Has PDF_KEY, 2) Is Featured, 3) Most recent
WITH ranked_books AS (
    SELECT 
        id,
        title,
        author,
        pdf_key,
        is_featured,
        created_at,
        ROW_NUMBER() OVER (
            ORDER BY 
                CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 ELSE 2 END,
                CASE WHEN is_featured THEN 1 ELSE 2 END,
                CASE WHEN LOWER(title) = 'hábitos atômicos' THEN 1 ELSE 2 END,
                created_at DESC
        ) as rank
    FROM books 
    WHERE LOWER(title) LIKE '%hábitos atômicos%' 
       OR LOWER(title) LIKE '%habitos atomicos%'
)
SELECT 
    'BOOK TO KEEP (RANK 1)' as step,
    id as keep_book_id,
    title,
    author,
    pdf_key,
    is_featured
FROM ranked_books 
WHERE rank = 1;

-- STEP 4: Remove user progress for duplicate books (keep only the best version)
DELETE FROM user_reading_progress 
WHERE book_id IN (
    WITH ranked_books AS (
        SELECT 
            id,
            ROW_NUMBER() OVER (
                ORDER BY 
                    CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 ELSE 2 END,
                    CASE WHEN is_featured THEN 1 ELSE 2 END,
                    CASE WHEN LOWER(title) = 'hábitos atômicos' THEN 1 ELSE 2 END,
                    created_at DESC
            ) as rank
        FROM books 
        WHERE LOWER(title) LIKE '%hábitos atômicos%' 
           OR LOWER(title) LIKE '%habitos atomicos%'
    )
    SELECT id::text FROM ranked_books WHERE rank > 1
);

-- STEP 5: Remove book contents for duplicate books
DELETE FROM book_contents 
WHERE book_id IN (
    WITH ranked_books AS (
        SELECT 
            id,
            ROW_NUMBER() OVER (
                ORDER BY 
                    CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 ELSE 2 END,
                    CASE WHEN is_featured THEN 1 ELSE 2 END,
                    CASE WHEN LOWER(title) = 'hábitos atômicos' THEN 1 ELSE 2 END,
                    created_at DESC
            ) as rank
        FROM books 
        WHERE LOWER(title) LIKE '%hábitos atômicos%' 
           OR LOWER(title) LIKE '%habitos atomicos%'
    )
    SELECT id FROM ranked_books WHERE rank > 1
);

-- STEP 6: Remove PDF files for duplicate books (if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'pdf_files' AND table_schema = 'public') THEN
        DELETE FROM pdf_files 
        WHERE book_id IN (
            WITH ranked_books AS (
                SELECT 
                    id,
                    ROW_NUMBER() OVER (
                        ORDER BY 
                            CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 ELSE 2 END,
                            CASE WHEN is_featured THEN 1 ELSE 2 END,
                            CASE WHEN LOWER(title) = 'hábitos atômicos' THEN 1 ELSE 2 END,
                            created_at DESC
                    ) as rank
                FROM books 
                WHERE LOWER(title) LIKE '%hábitos atômicos%' 
                   OR LOWER(title) LIKE '%habitos atomicos%'
            )
            SELECT id::text FROM ranked_books WHERE rank > 1
        );
        RAISE NOTICE 'PDF files for duplicate books removed';
    ELSE
        RAISE NOTICE 'pdf_files table does not exist - skipping';
    END IF;
END $$;

-- STEP 7: Remove duplicate book entries (keep only the best one)
DELETE FROM books 
WHERE id IN (
    WITH ranked_books AS (
        SELECT 
            id,
            title,
            ROW_NUMBER() OVER (
                ORDER BY 
                    CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 ELSE 2 END,
                    CASE WHEN is_featured THEN 1 ELSE 2 END,
                    CASE WHEN LOWER(title) = 'hábitos atômicos' THEN 1 ELSE 2 END,
                    created_at DESC
            ) as rank
        FROM books 
        WHERE LOWER(title) LIKE '%hábitos atômicos%' 
           OR LOWER(title) LIKE '%habitos atomicos%'
    )
    SELECT id FROM ranked_books WHERE rank > 1
);

-- STEP 8: Fix the remaining book to ensure proper PDF loading
UPDATE books 
SET 
    title = 'Hábitos Atômicos',
    author = 'James Clear',
    category = 'Desenvolvimento Pessoal',
    difficulty = 'Iniciante',
    is_featured = true,
    description = COALESCE(description, 'Um guia prático e científico para criar bons hábitos e eliminar os maus. James Clear apresenta estratégias comprovadas para transformar pequenas mudanças em grandes resultados.'),
    -- Ensure PDF key is set correctly (common filename patterns)
    pdf_key = CASE 
        WHEN pdf_key IS NULL OR pdf_key = '' THEN 'habitos_atomicos_james_clear.pdf'
        ELSE pdf_key
    END
WHERE LOWER(title) LIKE '%hábitos atômicos%' 
   OR LOWER(title) LIKE '%habitos atomicos%';

-- STEP 9: Remove any placeholder content from the remaining book
DELETE FROM book_contents 
WHERE book_id IN (
    SELECT id FROM books 
    WHERE LOWER(title) LIKE '%hábitos atômicos%' 
       OR LOWER(title) LIKE '%habitos atomicos%'
)
AND (
    content::text LIKE '%conteúdo completo está sendo processado%'
    OR content::text LIKE '%Nossa equipe está trabalhando%'
    OR content::text LIKE '%Aguarde o processamento completo%'
);

-- STEP 10: Verify the fix
SELECT 'VERIFICATION - FINAL STATE' as step;

SELECT 
    id,
    title,
    author,
    category,
    difficulty,
    duration,
    pdf_key,
    is_featured,
    description,
    CASE 
        WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN '✅ PDF_KEY SET'
        ELSE '❌ NO PDF_KEY'
    END as pdf_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM book_contents bc WHERE bc.book_id = books.id) THEN '⚠️ HAS CONTENT (may interfere with PDF)'
        ELSE '✅ NO CONTENT (will load PDF)'
    END as content_status
FROM books 
WHERE LOWER(title) LIKE '%hábitos atômicos%' 
   OR LOWER(title) LIKE '%habitos atomicos%';

-- STEP 11: Check if PDF exists in Storage (simulation)
SELECT 'PDF STORAGE CHECK' as step;

SELECT 
    title,
    pdf_key,
    'Test this URL in browser:' as instruction,
    'https://qmeelujsnpbcdkzhcwmm.supabase.co/storage/v1/object/public/books/' || pdf_key as test_url
FROM books 
WHERE LOWER(title) LIKE '%hábitos atômicos%' 
   OR LOWER(title) LIKE '%habitos atomicos%';

-- STEP 12: Final statistics
SELECT 'CLEANUP STATISTICS' as step;

SELECT 
    'Total Hábitos Atômicos books remaining' as metric,
    COUNT(*) as count
FROM books 
WHERE LOWER(title) LIKE '%hábitos atômicos%' 
   OR LOWER(title) LIKE '%habitos atomicos%'

UNION ALL

SELECT 
    'Books with PDF_KEY' as metric,
    COUNT(*) as count
FROM books 
WHERE (LOWER(title) LIKE '%hábitos atômicos%' OR LOWER(title) LIKE '%habitos atomicos%')
  AND pdf_key IS NOT NULL 
  AND pdf_key != ''

UNION ALL

SELECT 
    'Books with placeholder content' as metric,
    COUNT(*) as count
FROM books b
JOIN book_contents bc ON bc.book_id = b.id
WHERE (LOWER(b.title) LIKE '%hábitos atômicos%' OR LOWER(b.title) LIKE '%habitos atomicos%')
  AND (bc.content::text LIKE '%conteúdo completo está sendo processado%'
       OR bc.content::text LIKE '%Nossa equipe está trabalhando%');

-- Final success message
SELECT 
    '✅ HÁBITOS ATÔMICOS CLEANUP COMPLETED' as status,
    'Duplicate versions removed, PDF loading optimized' as result,
    'Test the book in the application now' as next_step;
