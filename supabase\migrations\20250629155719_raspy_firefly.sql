/*
  # Complete database schema setup

  1. Custom Types
    - stripe_order_status enum
    - stripe_subscription_status enum

  2. Tables
    - users (with auth integration)
    - books
    - token_transactions
    - user_book_access
    - reading_progress
    - collections
    - annotations
    - collection_books
    - study_sessions
    - flashcards
    - achievements
    - koreader_settings
    - koreader_bookmarks
    - koreader_highlights

  3. Security
    - RLS enabled on all tables
    - Appropriate policies for each table
    - User registration trigger
*/

-- Create custom types (handle existing types gracefully)
DO $$ BEGIN
    CREATE TYPE stripe_order_status AS ENUM ('pending', 'completed', 'canceled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE stripe_subscription_status AS ENUM ('not_started', 'incomplete', 'incomplete_expired', 'trialing', 'active', 'past_due', 'canceled', 'unpaid', 'paused');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create function for updating updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email text UNIQUE NOT NULL,
  name text,
  role text DEFAULT 'USER' NOT NULL,
  subscription_plan text DEFAULT 'FREE' NOT NULL,
  free_slots_available integer DEFAULT 5 NOT NULL,
  is_free_trial boolean DEFAULT true NOT NULL,
  trial_end_date timestamptz DEFAULT (now() + interval '30 days') NOT NULL,
  last_monthly_slot timestamptz DEFAULT now() NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL
);

-- Create books table
CREATE TABLE IF NOT EXISTS books (
  id integer PRIMARY KEY,
  created_at timestamptz DEFAULT now(),
  title text NOT NULL,
  author text,
  category text,
  pages integer,
  duration integer,
  description text,
  difficulty text,
  is_featured boolean DEFAULT false,
  is_free boolean DEFAULT false,
  pdf_key text
);

-- Create book_contents table
CREATE TABLE IF NOT EXISTS book_contents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  book_id integer NOT NULL REFERENCES books(id) ON DELETE CASCADE,
  content jsonb NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  UNIQUE(book_id)
);

-- Create token_transactions table
CREATE TABLE IF NOT EXISTS token_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  transaction_type text NOT NULL CHECK (transaction_type = ANY (ARRAY['PURCHASE', 'USAGE', 'REFUND', 'BONUS'])),
  amount integer NOT NULL,
  balance_after integer NOT NULL,
  description text,
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

-- Create user_book_access table
CREATE TABLE IF NOT EXISTS user_book_access (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  book_id uuid NOT NULL,
  accessed_at timestamptz DEFAULT now() NOT NULL,
  UNIQUE(user_id, book_id)
);

-- Create reading_progress table
CREATE TABLE IF NOT EXISTS reading_progress (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  book_id uuid NOT NULL,
  progress_percentage numeric(5,2) DEFAULT 0 NOT NULL,
  last_reading_position text,
  reading_time_minutes integer DEFAULT 0,
  last_read_at timestamptz DEFAULT now() NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  UNIQUE(user_id, book_id)
);

-- Create collections table
CREATE TABLE IF NOT EXISTS collections (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  name text NOT NULL,
  description text,
  color text DEFAULT 'blue',
  icon text DEFAULT 'book',
  is_public boolean DEFAULT false,
  tags text[] DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create annotations table
CREATE TABLE IF NOT EXISTS annotations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  book_id uuid NOT NULL,
  chapter_id text NOT NULL,
  text text NOT NULL,
  note text,
  color text DEFAULT 'yellow' CHECK (color = ANY (ARRAY['yellow', 'blue', 'green', 'red', 'purple'])),
  position jsonb NOT NULL,
  audio_timestamp integer,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create collection_books table
CREATE TABLE IF NOT EXISTS collection_books (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  collection_id uuid NOT NULL REFERENCES collections(id) ON DELETE CASCADE,
  book_id uuid NOT NULL,
  added_at timestamptz DEFAULT now(),
  UNIQUE(collection_id, book_id)
);

-- Create study_sessions table
CREATE TABLE IF NOT EXISTS study_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  book_id uuid NOT NULL,
  chapter_id text NOT NULL,
  session_type text NOT NULL CHECK (session_type = ANY (ARRAY['reading', 'listening', 'study', 'annotations', 'flashcards', 'review'])),
  duration_seconds integer DEFAULT 0,
  words_read integer DEFAULT 0,
  score integer DEFAULT 0,
  total_questions integer DEFAULT 0,
  correct_answers integer DEFAULT 0,
  concepts_learned text[] DEFAULT '{}',
  started_at timestamptz DEFAULT now(),
  completed_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- Create flashcards table
CREATE TABLE IF NOT EXISTS flashcards (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  book_id uuid NOT NULL,
  chapter_id text NOT NULL,
  question text NOT NULL,
  answer text NOT NULL,
  type text DEFAULT 'definition' CHECK (type = ANY (ARRAY['definition', 'concept', 'application', 'comparison'])),
  difficulty text DEFAULT 'medium' CHECK (difficulty = ANY (ARRAY['easy', 'medium', 'hard'])),
  concept_data jsonb DEFAULT '{}',
  times_shown integer DEFAULT 0,
  times_correct integer DEFAULT 0,
  last_shown timestamptz,
  next_review timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create achievements table
CREATE TABLE IF NOT EXISTS achievements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  achievement_id text NOT NULL,
  title text NOT NULL,
  description text NOT NULL,
  icon text NOT NULL,
  category text NOT NULL CHECK (category = ANY (ARRAY['reading', 'study', 'streak', 'speed', 'completion'])),
  rarity text DEFAULT 'common' CHECK (rarity = ANY (ARRAY['common', 'rare', 'epic', 'legendary'])),
  unlocked_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, achievement_id)
);

-- Create koreader_settings table
CREATE TABLE IF NOT EXISTS koreader_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  theme varchar(20) DEFAULT 'light',
  font_size integer DEFAULT 16,
  font_family varchar(50) DEFAULT 'Georgia',
  line_height numeric(3,2) DEFAULT 1.5,
  margin_size integer DEFAULT 20,
  scroll_behavior varchar(20) DEFAULT 'smooth',
  show_progress boolean DEFAULT true,
  enable_gestures boolean DEFAULT true,
  auto_save_interval integer DEFAULT 30,
  language varchar(10) DEFAULT 'pt-BR',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id)
);

-- Create koreader_bookmarks table
CREATE TABLE IF NOT EXISTS koreader_bookmarks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  book_id integer NOT NULL REFERENCES books(id) ON DELETE CASCADE,
  page_number integer NOT NULL,
  position jsonb,
  label text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create koreader_highlights table
CREATE TABLE IF NOT EXISTS koreader_highlights (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  book_id integer NOT NULL REFERENCES books(id) ON DELETE CASCADE,
  page_number integer NOT NULL,
  text text NOT NULL,
  color varchar(7) DEFAULT '#ffeb3b',
  start_position jsonb,
  end_position jsonb,
  note text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE books ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_contents ENABLE ROW LEVEL SECURITY;
ALTER TABLE token_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_book_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE reading_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE annotations ENABLE ROW LEVEL SECURITY;
ALTER TABLE collection_books ENABLE ROW LEVEL SECURITY;
ALTER TABLE study_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE flashcards ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE koreader_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE koreader_bookmarks ENABLE ROW LEVEL SECURITY;
ALTER TABLE koreader_highlights ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "users_select_own" ON users;
DROP POLICY IF EXISTS "Admins can view all users" ON users;
DROP POLICY IF EXISTS "Admins can update any user" ON users;
DROP POLICY IF EXISTS "Allow public read access on books" ON books;
DROP POLICY IF EXISTS "Admins can manage books" ON books;
DROP POLICY IF EXISTS "Users can view own transactions" ON token_transactions;
DROP POLICY IF EXISTS "Users can insert own transactions" ON token_transactions;
DROP POLICY IF EXISTS "access_select_own" ON user_book_access;
DROP POLICY IF EXISTS "Users can view own collections" ON collections;
DROP POLICY IF EXISTS "Users can view public collections" ON collections;
DROP POLICY IF EXISTS "Users can insert own collections" ON collections;
DROP POLICY IF EXISTS "Users can update own collections" ON collections;
DROP POLICY IF EXISTS "Users can delete own collections" ON collections;
DROP POLICY IF EXISTS "Users can view own annotations" ON annotations;
DROP POLICY IF EXISTS "Users can insert own annotations" ON annotations;
DROP POLICY IF EXISTS "Users can update own annotations" ON annotations;
DROP POLICY IF EXISTS "Users can delete own annotations" ON annotations;
DROP POLICY IF EXISTS "Users can view own sessions" ON study_sessions;
DROP POLICY IF EXISTS "Users can insert own sessions" ON study_sessions;
DROP POLICY IF EXISTS "Users can view own flashcards" ON flashcards;
DROP POLICY IF EXISTS "Users can insert own flashcards" ON flashcards;
DROP POLICY IF EXISTS "Users can update own flashcards" ON flashcards;
DROP POLICY IF EXISTS "Users can delete own flashcards" ON flashcards;
DROP POLICY IF EXISTS "Users can view own achievements" ON achievements;
DROP POLICY IF EXISTS "Users can insert own achievements" ON achievements;
DROP POLICY IF EXISTS "Users can manage own settings" ON koreader_settings;
DROP POLICY IF EXISTS "Users can manage own bookmarks" ON koreader_bookmarks;
DROP POLICY IF EXISTS "Users can manage own highlights" ON koreader_highlights;

-- Create policies for users table
CREATE POLICY "users_select_own" ON users FOR SELECT TO public USING (auth.uid() = id);
CREATE POLICY "Admins can view all users" ON users FOR SELECT TO authenticated USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'ADMIN')
);
CREATE POLICY "Admins can update any user" ON users FOR UPDATE TO authenticated USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'ADMIN')
);

-- Create policies for books table
CREATE POLICY "Allow public read access on books" ON books FOR SELECT TO public USING (true);
CREATE POLICY "Admins can manage books" ON books FOR ALL TO authenticated USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'ADMIN')
);

-- Create policies for book_contents table
CREATE POLICY "Allow public read access on book_contents" ON book_contents FOR SELECT TO public USING (true);
CREATE POLICY "Admins can manage book_contents" ON book_contents FOR ALL TO authenticated USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'ADMIN')
);

-- Create policies for token_transactions table
CREATE POLICY "Users can view own transactions" ON token_transactions FOR SELECT TO public USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own transactions" ON token_transactions FOR INSERT TO public WITH CHECK (auth.uid() = user_id);

-- Create policies for user_book_access table
CREATE POLICY "access_select_own" ON user_book_access FOR SELECT TO public USING (auth.uid() = user_id);

-- Create policies for collections table
CREATE POLICY "Users can view own collections" ON collections FOR SELECT TO public USING (auth.uid() = user_id);
CREATE POLICY "Users can view public collections" ON collections FOR SELECT TO public USING (is_public = true);
CREATE POLICY "Users can insert own collections" ON collections FOR INSERT TO public WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own collections" ON collections FOR UPDATE TO public USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own collections" ON collections FOR DELETE TO public USING (auth.uid() = user_id);

-- Create policies for annotations table
CREATE POLICY "Users can view own annotations" ON annotations FOR SELECT TO public USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own annotations" ON annotations FOR INSERT TO public WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own annotations" ON annotations FOR UPDATE TO public USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own annotations" ON annotations FOR DELETE TO public USING (auth.uid() = user_id);

-- Create policies for study_sessions table
CREATE POLICY "Users can view own sessions" ON study_sessions FOR SELECT TO public USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own sessions" ON study_sessions FOR INSERT TO public WITH CHECK (auth.uid() = user_id);

-- Create policies for flashcards table
CREATE POLICY "Users can view own flashcards" ON flashcards FOR SELECT TO public USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own flashcards" ON flashcards FOR INSERT TO public WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own flashcards" ON flashcards FOR UPDATE TO public USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own flashcards" ON flashcards FOR DELETE TO public USING (auth.uid() = user_id);

-- Create policies for achievements table
CREATE POLICY "Users can view own achievements" ON achievements FOR SELECT TO public USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own achievements" ON achievements FOR INSERT TO public WITH CHECK (auth.uid() = user_id);

-- Create policies for koreader_settings table
CREATE POLICY "Users can manage own settings" ON koreader_settings FOR ALL TO public USING (auth.uid() = user_id);

-- Create policies for koreader_bookmarks table
CREATE POLICY "Users can manage own bookmarks" ON koreader_bookmarks FOR ALL TO public USING (auth.uid() = user_id);

-- Create policies for koreader_highlights table
CREATE POLICY "Users can manage own highlights" ON koreader_highlights FOR ALL TO public USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_book_contents_book_id ON book_contents(book_id);
CREATE INDEX IF NOT EXISTS idx_token_transactions_user_id ON token_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_annotations_user_id ON annotations(user_id);
CREATE INDEX IF NOT EXISTS idx_annotations_book_id ON annotations(book_id);
CREATE INDEX IF NOT EXISTS idx_study_sessions_user_id ON study_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_study_sessions_created_at ON study_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_flashcards_user_id ON flashcards(user_id);
CREATE INDEX IF NOT EXISTS idx_flashcards_next_review ON flashcards(next_review);
CREATE INDEX IF NOT EXISTS idx_settings_user ON koreader_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_user_book ON koreader_bookmarks(user_id, book_id);
CREATE INDEX IF NOT EXISTS idx_highlights_user_book ON koreader_highlights(user_id, book_id);

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_book_contents_updated_at ON book_contents;
CREATE TRIGGER update_book_contents_updated_at BEFORE UPDATE ON book_contents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_collections_updated_at ON collections;
CREATE TRIGGER update_collections_updated_at BEFORE UPDATE ON collections FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_annotations_updated_at ON annotations;
CREATE TRIGGER update_annotations_updated_at BEFORE UPDATE ON annotations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_flashcards_updated_at ON flashcards;
CREATE TRIGGER update_flashcards_updated_at BEFORE UPDATE ON flashcards FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_koreader_settings_updated_at ON koreader_settings;
CREATE TRIGGER update_koreader_settings_updated_at BEFORE UPDATE ON koreader_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_koreader_bookmarks_updated_at ON koreader_bookmarks;
CREATE TRIGGER update_koreader_bookmarks_updated_at BEFORE UPDATE ON koreader_bookmarks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_koreader_highlights_updated_at ON koreader_highlights;
CREATE TRIGGER update_koreader_highlights_updated_at BEFORE UPDATE ON koreader_highlights FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', NEW.email)
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();