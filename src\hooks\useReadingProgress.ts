import { useState, useEffect, useCallback } from 'react';
import { dataService } from '@/lib/dataService';

interface ReadingProgressState {
  bookId: string;
  progress: number;
  isCompleted: boolean;
  isFavorited: boolean;
  lastReadAt: string;
}

interface UseReadingProgressReturn {
  progressState: ReadingProgressState | null;
  updateProgress: (progress: number) => Promise<void>;
  toggleFavorite: () => Promise<void>;
  markAsCompleted: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export function useReadingProgress(bookId: string): UseReadingProgressReturn {
  const [progressState, setProgressState] = useState<ReadingProgressState | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load initial progress state
  useEffect(() => {
    if (!bookId) return;

    const loadProgress = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const progress = await dataService.getSummaryProgress(bookId);

        if (progress) {
          setProgressState({
            bookId,
            progress: progress.progress_percentage,
            isCompleted: progress.is_completed,
            isFavorited: progress.is_favorited,
            lastReadAt: progress.last_read_at
          });
        } else {
          // Initialize with default state if no progress exists
          setProgressState({
            bookId,
            progress: 0,
            isCompleted: false,
            isFavorited: false,
            lastReadAt: new Date().toISOString()
          });
        }


      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load progress');
        console.error('Error loading reading progress:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadProgress();
  }, [bookId]);

  // Update reading progress
  const updateProgress = useCallback(async (progress: number) => {
    if (!bookId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Get current page info to maintain proper progress tracking
      const currentPageInfo = await dataService.getCurrentPage(bookId);
      const currentPage = currentPageInfo?.currentPage || 1;
      const totalPages = currentPageInfo?.totalPages || 1;

      const updatedProgress = await dataService.updateProgress(
        bookId,
        progress,
        currentPage,
        totalPages,
        0 // reading time will be updated by session
      );

      setProgressState(prev => prev ? {
        ...prev,
        progress: updatedProgress.progress_percentage,
        isCompleted: updatedProgress.is_completed,
        lastReadAt: updatedProgress.last_read_at
      } : null);


    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update progress');
      console.error('Error updating progress:', err);
    } finally {
      setIsLoading(false);
    }
  }, [bookId]);

  // Toggle favorite status
  const toggleFavorite = useCallback(async () => {
    if (!bookId || !progressState) return;

    setIsLoading(true);
    setError(null);

    try {
      const updatedProgress = await dataService.toggleFavorite(bookId, !progressState.isFavorited);

      setProgressState(prev => prev ? {
        ...prev,
        isFavorited: updatedProgress.is_favorited
      } : null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to toggle favorite');
      console.error('Error toggling favorite:', err);
    } finally {
      setIsLoading(false);
    }
  }, [bookId, progressState]);

  // Mark as completed
  const markAsCompleted = useCallback(async () => {
    if (!bookId) return;

    await updateProgress(100);
  }, [bookId, updateProgress]);

  return {
    progressState,
    updateProgress,
    toggleFavorite,
    markAsCompleted,
    isLoading,
    error
  };
}

// Hook for managing reading sequence preferences
interface ReadingSequencePreferences {
  autoAdvance: boolean;
  readingSpeed: 'slow' | 'normal' | 'fast';
  fontSize: 'small' | 'medium' | 'large';
  theme: 'light' | 'dark' | 'sepia';
}

interface UseReadingSequenceReturn {
  preferences: ReadingSequencePreferences;
  updatePreferences: (newPreferences: Partial<ReadingSequencePreferences>) => void;
  resetPreferences: () => void;
}

const DEFAULT_PREFERENCES: ReadingSequencePreferences = {
  autoAdvance: false,
  readingSpeed: 'normal',
  fontSize: 'medium',
  theme: 'light'
};

export function useReadingSequence(): UseReadingSequenceReturn {
  const [preferences, setPreferences] = useState<ReadingSequencePreferences>(DEFAULT_PREFERENCES);

  // Load preferences from localStorage on mount
  useEffect(() => {
    const savedPreferences = localStorage.getItem('reading-sequence-preferences');
    if (savedPreferences) {
      try {
        const parsed = JSON.parse(savedPreferences);
        setPreferences({ ...DEFAULT_PREFERENCES, ...parsed });
      } catch (error) {
        console.error('Error parsing saved preferences:', error);
      }
    }
  }, []);

  // Save preferences to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('reading-sequence-preferences', JSON.stringify(preferences));
  }, [preferences]);

  const updatePreferences = useCallback((newPreferences: Partial<ReadingSequencePreferences>) => {
    setPreferences(prev => ({ ...prev, ...newPreferences }));
  }, []);

  const resetPreferences = useCallback(() => {
    setPreferences(DEFAULT_PREFERENCES);
    localStorage.removeItem('reading-sequence-preferences');
  }, []);

  return {
    preferences,
    updatePreferences,
    resetPreferences
  };
}

// Hook for managing reading session state
interface ReadingSession {
  startTime: Date;
  currentPage: number;
  totalPages: number;
  timeSpent: number; // in seconds
  wordsRead: number;
}

interface UseReadingSessionReturn {
  session: ReadingSession | null;
  startSession: (totalPages: number) => void;
  updateSession: (currentPage: number, wordsRead?: number) => void;
  endSession: () => Promise<void>;
  pauseSession: () => void;
  resumeSession: () => void;
  isPaused: boolean;
}

export function useReadingSession(bookId: string): UseReadingSessionReturn {
  const [session, setSession] = useState<ReadingSession | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

  const startSession = useCallback((totalPages: number) => {
    const newSession: ReadingSession = {
      startTime: new Date(),
      currentPage: 1,
      totalPages,
      timeSpent: 0,
      wordsRead: 0
    };

    setSession(newSession);
    setIsPaused(false);

    // Start timer
    const id = setInterval(() => {
      if (!isPaused) {
        setSession(prev => prev ? { ...prev, timeSpent: prev.timeSpent + 1 } : null);
      }
    }, 1000);

    setIntervalId(id);
  }, [isPaused]);

  const updateSession = useCallback((currentPage: number, wordsRead?: number) => {
    setSession(prev => prev ? {
      ...prev,
      currentPage,
      wordsRead: wordsRead ?? prev.wordsRead
    } : null);
  }, []);

  const endSession = useCallback(async () => {
    if (!session || !bookId) return;

    // Clear timer
    if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }

    // Calculate final progress
    const progressPercentage = Math.round((session.currentPage / session.totalPages) * 100);

    try {
      // Update progress in database with correct parameters
      await dataService.updateProgress(
        bookId,
        progressPercentage,
        session.currentPage,
        session.totalPages,
        session.timeSpent
      );


    } catch (error) {
      console.error('Error saving reading session:', error);
    }

    setSession(null);
    setIsPaused(false);
  }, [session, bookId, intervalId]);

  const pauseSession = useCallback(() => {
    setIsPaused(true);
  }, []);

  const resumeSession = useCallback(() => {
    setIsPaused(false);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [intervalId]);

  return {
    session,
    startSession,
    updateSession,
    endSession,
    pauseSession,
    resumeSession,
    isPaused
  };
}
