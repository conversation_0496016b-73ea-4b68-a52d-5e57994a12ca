import React, { useEffect, useState } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { CheckCircle, XCircle, Loader2 } from 'lucide-react'
import { dataService } from '../../lib/dataService'

interface SubscriptionResultProps {
  type: 'success' | 'cancel'
}

export function SubscriptionResult({ type }: SubscriptionResultProps) {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const sessionId = searchParams.get('session_id')

  useEffect(() => {
    if (type === 'success' && sessionId) {
      // Verify the session and update subscription status
      verifySubscription()
    } else {
      setLoading(false)
    }
  }, [type, sessionId])

  const verifySubscription = async () => {
    try {
      // In a real implementation, you would verify the session with your backend
      // For now, we'll just refresh the dashboard data
      await dataService.getDashboardData()
      setLoading(false)
    } catch (err) {
      console.error('Error verifying subscription:', err)
      setError('Failed to verify subscription status')
      setLoading(false)
    }
  }

  const handleContinue = () => {
    navigate('/dashboard')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto" />
              <p>Verificando status da assinatura...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            {type === 'success' ? (
              <CheckCircle className="h-16 w-16 text-green-500" />
            ) : (
              <XCircle className="h-16 w-16 text-red-500" />
            )}
          </div>
          <CardTitle className="text-2xl">
            {type === 'success' ? 'Assinatura Confirmada!' : 'Assinatura Cancelada'}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="text-center space-y-4">
          {type === 'success' ? (
            <div className="space-y-4">
              <p className="text-gray-600">
                Parabéns! Sua assinatura foi ativada com sucesso. 
                Agora você tem acesso completo a todos os recursos premium.
              </p>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-semibold text-green-800 mb-2">
                  O que você ganhou:
                </h3>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• Resumos ilimitados</li>
                  <li>• Acesso a conteúdo premium</li>
                  <li>• Download para leitura offline</li>
                  <li>• Suporte prioritário</li>
                  <li>• Estatísticas avançadas</li>
                </ul>
              </div>

              {error && (
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <p className="text-yellow-800 text-sm">
                    ⚠️ {error}
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-gray-600">
                Sua assinatura foi cancelada. Não se preocupe, você ainda pode 
                usar nossa versão gratuita com 5 resumos por mês.
              </p>
              
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-semibold text-blue-800 mb-2">
                  Ainda disponível para você:
                </h3>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• 5 resumos gratuitos por mês</li>
                  <li>• Acesso básico à biblioteca</li>
                  <li>• Progresso de leitura</li>
                  <li>• Sistema de favoritos</li>
                </ul>
              </div>
              
              <p className="text-sm text-gray-500">
                Você pode fazer upgrade a qualquer momento para ter acesso 
                completo aos recursos premium.
              </p>
            </div>
          )}

          <Button 
            onClick={handleContinue}
            className="w-full"
            size="lg"
          >
            {type === 'success' ? 'Começar a Usar' : 'Voltar ao Dashboard'}
          </Button>

          {type === 'cancel' && (
            <Button 
              onClick={() => navigate('/subscription')}
              variant="outline"
              className="w-full"
            >
              Ver Planos Disponíveis
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// Success page component
export function SubscriptionSuccess() {
  return <SubscriptionResult type="success" />
}

// Cancel page component  
export function SubscriptionCancel() {
  return <SubscriptionResult type="cancel" />
}
