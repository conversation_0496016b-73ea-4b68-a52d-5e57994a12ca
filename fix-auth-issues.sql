-- =====================================================
-- CORREÇÃO DE PROBLEMAS DE AUTENTICAÇÃO
-- Execute este script no SQL Editor do Supabase
-- =====================================================

-- 1. VERIFICAR CONFIGURAÇÕES ATUAIS
SELECT 'VERIFICAÇÃO INICIAL' as step;

-- Verificar se a tabela users existe e tem as colunas corretas
SELECT 
    'users table structure' as info,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

-- Verificar triggers existentes
SELECT 
    'auth triggers' as info,
    trigger_name,
    event_manipulation,
    action_statement
FROM information_schema.triggers 
WHERE trigger_name LIKE '%auth%' OR trigger_name LIKE '%user%';

-- 2. RECRIAR FUNÇÃO DE CRIAÇÃO DE USUÁRIO (VERSÃO SIMPLIFICADA)
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Log para debug
  RAISE LOG 'Creating user profile for: %', NEW.email;
  
  -- Inserir na tabela users (se ela existir)
  BEGIN
    INSERT INTO public.users (id, email, name, created_at, updated_at)
    VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
      NOW(),
      NOW()
    )
    ON CONFLICT (id) DO NOTHING;
    
    RAISE LOG 'User profile created successfully for: %', NEW.email;
  EXCEPTION WHEN OTHERS THEN
    RAISE LOG 'Error creating user profile for %: %', NEW.email, SQLERRM;
    -- Não falhar o registro se houver erro na criação do perfil
  END;
  
  -- Criar assinatura padrão (se a tabela existir)
  BEGIN
    INSERT INTO public.user_subscriptions (user_id, subscription_type, status, remaining_free_access)
    VALUES (NEW.id, 'free', 'active', 5)
    ON CONFLICT (user_id) DO NOTHING;
    
    RAISE LOG 'User subscription created for: %', NEW.email;
  EXCEPTION WHEN OTHERS THEN
    RAISE LOG 'Error creating subscription for %: %', NEW.email, SQLERRM;
    -- Não falhar o registro se houver erro na criação da assinatura
  END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. RECRIAR TRIGGER
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- 4. VERIFICAR E CRIAR TABELAS NECESSÁRIAS SE NÃO EXISTIREM

-- Criar tabela users se não existir
CREATE TABLE IF NOT EXISTS public.users (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email text UNIQUE NOT NULL,
  name text,
  role text DEFAULT 'USER' NOT NULL,
  subscription_plan text DEFAULT 'FREE' NOT NULL,
  free_slots_available integer DEFAULT 5 NOT NULL,
  is_free_trial boolean DEFAULT true NOT NULL,
  trial_end_date timestamptz DEFAULT (now() + interval '30 days') NOT NULL,
  last_monthly_slot timestamptz DEFAULT now() NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL
);

-- Criar tabela user_subscriptions se não existir
CREATE TABLE IF NOT EXISTS public.user_subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_type text NOT NULL DEFAULT 'free' CHECK (subscription_type IN ('free', 'premium')),
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'expired')),
  started_at timestamptz DEFAULT now() NOT NULL,
  expires_at timestamptz,
  remaining_free_access integer DEFAULT 5 NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  UNIQUE(user_id)
);

-- 5. HABILITAR RLS E CRIAR POLÍTICAS BÁSICAS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;

-- Políticas para users
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Políticas para user_subscriptions
DROP POLICY IF EXISTS "Users can view own subscription" ON public.user_subscriptions;
CREATE POLICY "Users can view own subscription" ON public.user_subscriptions
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own subscription" ON public.user_subscriptions;
CREATE POLICY "Users can update own subscription" ON public.user_subscriptions
  FOR UPDATE USING (auth.uid() = user_id);

-- 6. TESTAR A FUNÇÃO MANUALMENTE
SELECT 'TESTE DA FUNÇÃO' as step;

-- Simular criação de usuário para teste
DO $$
DECLARE
  test_user_id uuid := gen_random_uuid();
  test_email text := '<EMAIL>';
BEGIN
  -- Simular trigger
  RAISE NOTICE 'Testando função handle_new_user...';
  
  -- Criar um registro temporário para teste
  INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at)
  VALUES (
    test_user_id,
    test_email,
    'dummy_password',
    NOW(),
    NOW(),
    NOW()
  );
  
  -- Verificar se foi criado
  IF EXISTS (SELECT 1 FROM public.users WHERE id = test_user_id) THEN
    RAISE NOTICE '✅ Usuário criado com sucesso na tabela users';
  ELSE
    RAISE NOTICE '❌ Falha ao criar usuário na tabela users';
  END IF;
  
  IF EXISTS (SELECT 1 FROM public.user_subscriptions WHERE user_id = test_user_id) THEN
    RAISE NOTICE '✅ Assinatura criada com sucesso';
  ELSE
    RAISE NOTICE '❌ Falha ao criar assinatura';
  END IF;
  
  -- Limpar dados de teste
  DELETE FROM auth.users WHERE id = test_user_id;
  DELETE FROM public.users WHERE id = test_user_id;
  DELETE FROM public.user_subscriptions WHERE user_id = test_user_id;
  
  RAISE NOTICE 'Teste concluído e dados limpos';
END $$;

-- 7. VERIFICAÇÃO FINAL
SELECT 'VERIFICAÇÃO FINAL' as step;

-- Contar usuários existentes
SELECT 
    'user_counts' as info,
    (SELECT COUNT(*) FROM auth.users) as auth_users,
    (SELECT COUNT(*) FROM public.users) as public_users,
    (SELECT COUNT(*) FROM public.user_subscriptions) as subscriptions;

-- Verificar triggers ativos
SELECT 
    'active_triggers' as info,
    trigger_name,
    event_manipulation
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

SELECT 'CORREÇÃO CONCLUÍDA' as final_step;
