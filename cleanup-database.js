import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Function to identify problematic books
function isProblematicBook(book) {
  const title = book.title?.toLowerCase().trim() || '';
  const author = book.author?.toLowerCase().trim() || '';

  return (
    // Very short or single letter titles
    title.length <= 3 ||
    title === 'a' || title === 'o' || title === 'e o' || title === 'um' ||

    // Titles ending with numbers (indicating duplicates/poor formatting)
    title.includes(' 3') || title.includes(' 5') || title.includes(' 6') ||

    // Unknown authors
    author === 'autor desconhecido' ||
    author === 'desconhecido 3' ||

    // Specific problematic patterns
    title.includes('579 courses') ||
    title.includes('resumo psicoterapias') ||
    title.includes('historia psicologia moderna resumo') ||

    // Books with malformed titles
    title.startsWith('e o ') ||
    title.startsWith('o eu e o ') ||
    /^[aeo]\s/.test(title) || // Single letters followed by space

    // Books marked as removed
    title.startsWith('[removed]') ||
    book.category === 'REMOVED' ||
    book.category === 'Geral' // Hide all books in "Geral" category as they're mostly problematic
  );
}

// Function to normalize title for duplicate detection
function normalizeTitle(title) {
  return title
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .replace(/\s+/g, ' ')
    .replace(/\b(versao|completa|parafrase|os melhores discursos)\b/g, '')
    .trim();
}

// Function to find duplicates
function findDuplicates(books) {
  const titleGroups = {};
  const duplicates = [];

  // Group books by normalized title
  books.forEach(book => {
    const normalizedTitle = normalizeTitle(book.title);
    if (!titleGroups[normalizedTitle]) {
      titleGroups[normalizedTitle] = [];
    }
    titleGroups[normalizedTitle].push(book);
  });

  // Find groups with multiple books (duplicates)
  Object.entries(titleGroups).forEach(([normalizedTitle, group]) => {
    if (group.length > 1) {
      // Sort by quality and keep the best one, mark others for removal
      const sortedGroup = group.sort((a, b) => {
        let scoreA = 0, scoreB = 0;

        // Prefer proper authors
        if (a.author && a.author !== 'Autor Desconhecido') scoreA += 10;
        if (b.author && b.author !== 'Autor Desconhecido') scoreB += 10;

        // Prefer shorter, cleaner titles
        if (a.title.length < b.title.length) scoreA += 5;
        if (b.title.length < a.title.length) scoreB += 5;

        // Prefer books without parentheses
        if (!a.title.includes('(')) scoreA += 3;
        if (!b.title.includes('(')) scoreB += 3;

        // Prefer featured books
        if (a.is_featured) scoreA += 2;
        if (b.is_featured) scoreB += 2;

        return scoreB - scoreA;
      });

      // Keep the first (best) book, mark others as duplicates
      for (let i = 1; i < sortedGroup.length; i++) {
        duplicates.push({
          ...sortedGroup[i],
          reason: `Duplicate of: ${sortedGroup[0].title} (ID: ${sortedGroup[0].id})`
        });
      }
    }
  });

  return duplicates;
}

async function cleanupDatabase() {
  try {
    console.log('🧹 Starting database cleanup...');

    // Fetch all books
    const { data: books, error } = await supabase
      .from('books')
      .select('*')
      .order('id');

    if (error) {
      throw error;
    }

    console.log(`📚 Found ${books.length} total books in database`);

    // Identify problematic books
    const problematicBooks = books.filter(isProblematicBook);
    console.log(`🚫 Found ${problematicBooks.length} problematic books`);

    // Find duplicates among clean books
    const cleanBooks = books.filter(book => !isProblematicBook(book));
    const duplicates = findDuplicates(cleanBooks);
    console.log(`🔄 Found ${duplicates.length} duplicate books`);

    // Combine all books to be removed
    const booksToRemove = [...problematicBooks, ...duplicates];
    console.log(`🗑️  Total books to remove: ${booksToRemove.length}`);

    if (booksToRemove.length === 0) {
      console.log('✅ No books need to be removed');
      return;
    }

    // Log books that will be removed
    console.log('\n📋 Books to be removed:');
    booksToRemove.forEach((book, index) => {
      console.log(`${index + 1}. ID: ${book.id} - "${book.title}" by ${book.author}`);
      if (book.reason) {
        console.log(`   Reason: ${book.reason}`);
      }
    });

    // Ask for confirmation
    console.log('\n⚠️  This will permanently delete these books from the database.');
    console.log('Press Ctrl+C to cancel, or any key to continue...');
    
    // Wait for user input (in a real scenario, you'd use readline)
    // For now, we'll just log what would be done
    console.log('\n🔧 DRY RUN - Books that would be deleted:');
    
    const idsToDelete = booksToRemove.map(book => book.id);
    console.log(`Book IDs: ${idsToDelete.join(', ')}`);

    // Uncomment the following lines to actually delete the books
    /*
    const { error: deleteError } = await supabase
      .from('books')
      .delete()
      .in('id', idsToDelete);

    if (deleteError) {
      throw deleteError;
    }

    console.log(`✅ Successfully deleted ${booksToRemove.length} books`);
    */

    console.log('\n📊 Final statistics:');
    console.log(`- Original books: ${books.length}`);
    console.log(`- Problematic books: ${problematicBooks.length}`);
    console.log(`- Duplicate books: ${duplicates.length}`);
    console.log(`- Books to keep: ${books.length - booksToRemove.length}`);

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  }
}

// Handle specific Gestalt-Terapia book issues
async function fixGestaltTerapiaBook() {
  try {
    console.log('🔧 Checking Gestalt-Terapia book...');

    const { data: gestaltBooks, error } = await supabase
      .from('books')
      .select('*')
      .ilike('title', '%gestalt%terapia%');

    if (error) {
      throw error;
    }

    console.log(`Found ${gestaltBooks.length} Gestalt-Terapia related books`);

    gestaltBooks.forEach(book => {
      console.log(`- ID: ${book.id}, Title: "${book.title}", Author: ${book.author}`);
    });

    // Check if there are issues with the book content
    if (gestaltBooks.length > 0) {
      const bookId = gestaltBooks[0].id;
      
      const { data: content, error: contentError } = await supabase
        .from('book_contents')
        .select('*')
        .eq('book_id', bookId);

      if (contentError) {
        console.log('⚠️  Error fetching book content:', contentError);
      } else if (!content || content.length === 0) {
        console.log('⚠️  No content found for Gestalt-Terapia book');
      } else {
        console.log('✅ Gestalt-Terapia book has content');
      }
    }

  } catch (error) {
    console.error('❌ Error checking Gestalt-Terapia book:', error);
  }
}

// Main execution
async function main() {
  console.log('🚀 Database Cleanup Tool');
  console.log('========================\n');

  await cleanupDatabase();
  await fixGestaltTerapiaBook();

  console.log('\n✅ Cleanup analysis complete!');
  console.log('To actually delete the books, uncomment the deletion code in the script.');
}

main().catch(console.error);
