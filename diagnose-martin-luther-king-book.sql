-- Diagnóstico do livro "Um Apelo à Consciência" de <PERSON> Jr.
-- Execute este SQL no Supabase Dashboard > SQL Editor

-- STEP 1: Encontrar o livro específico
SELECT 'ENCONTRANDO O LIVRO' as step;

SELECT 
    id,
    title,
    author,
    category,
    description,
    pdf_key,
    pages,
    duration,
    is_featured,
    created_at
FROM books 
WHERE LOWER(title) LIKE '%apelo%consciência%' 
   OR LOWER(title) LIKE '%apelo%consciencia%'
   OR (LOWER(title) LIKE '%martin luther king%' AND LOWER(title) LIKE '%discursos%')
   OR (LOWER(author) LIKE '%martin luther king%' AND LOWER(title) LIKE '%melhores%')
ORDER BY created_at DESC;

-- STEP 2: Verificar se há conteúdo na tabela book_contents
SELECT 'VERIFICANDO CONTEÚDO NA TABELA book_contents' as step;

SELECT 
    bc.id,
    bc.book_id,
    b.title,
    CASE 
        WHEN bc.content IS NOT NULL THEN 'TEM CONTEÚDO'
        ELSE 'SEM CONTEÚDO'
    END as content_status,
    CASE 
        WHEN bc.content IS NOT NULL THEN LENGTH(bc.content::text)
        ELSE 0
    END as content_length,
    bc.created_at
FROM book_contents bc
RIGHT JOIN books b ON b.id = bc.book_id
WHERE LOWER(b.title) LIKE '%apelo%consciência%' 
   OR LOWER(b.title) LIKE '%apelo%consciencia%'
   OR (LOWER(b.title) LIKE '%martin luther king%' AND LOWER(b.title) LIKE '%discursos%')
   OR (LOWER(b.author) LIKE '%martin luther king%' AND LOWER(b.title) LIKE '%melhores%');

-- STEP 3: Verificar se há PDF na tabela pdf_files
SELECT 'VERIFICANDO PDF NA TABELA pdf_files' as step;

SELECT 
    pf.id,
    pf.book_id,
    b.title,
    pf.filename,
    pf.file_size,
    CASE 
        WHEN pf.file_data IS NOT NULL THEN 'TEM PDF'
        ELSE 'SEM PDF'
    END as pdf_status,
    pf.created_at
FROM pdf_files pf
RIGHT JOIN books b ON b.id::text = pf.book_id
WHERE LOWER(b.title) LIKE '%apelo%consciência%' 
   OR LOWER(b.title) LIKE '%apelo%consciencia%'
   OR (LOWER(b.title) LIKE '%martin luther king%' AND LOWER(b.title) LIKE '%discursos%')
   OR (LOWER(b.author) LIKE '%martin luther king%' AND LOWER(b.title) LIKE '%melhores%');

-- STEP 4: Verificar se há pdf_key no livro
SELECT 'VERIFICANDO PDF_KEY NO LIVRO' as step;

SELECT 
    id,
    title,
    author,
    pdf_key,
    CASE 
        WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 'TEM PDF_KEY'
        ELSE 'SEM PDF_KEY'
    END as pdf_key_status
FROM books 
WHERE LOWER(title) LIKE '%apelo%consciência%' 
   OR LOWER(title) LIKE '%apelo%consciencia%'
   OR (LOWER(title) LIKE '%martin luther king%' AND LOWER(title) LIKE '%discursos%')
   OR (LOWER(author) LIKE '%martin luther king%' AND LOWER(title) LIKE '%melhores%');

-- STEP 5: Buscar por outros livros de Martin Luther King que funcionam
SELECT 'OUTROS LIVROS DE MARTIN LUTHER KING' as step;

SELECT 
    b.id,
    b.title,
    b.author,
    b.pdf_key,
    CASE 
        WHEN bc.content IS NOT NULL THEN 'TEM CONTEÚDO'
        ELSE 'SEM CONTEÚDO'
    END as content_status,
    CASE 
        WHEN pf.file_data IS NOT NULL THEN 'TEM PDF'
        ELSE 'SEM PDF'
    END as pdf_status
FROM books b
LEFT JOIN book_contents bc ON bc.book_id = b.id
LEFT JOIN pdf_files pf ON pf.book_id = b.id::text
WHERE LOWER(b.author) LIKE '%martin luther king%'
   OR LOWER(b.title) LIKE '%martin luther%'
ORDER BY b.created_at DESC;

-- STEP 6: Verificar se há duplicatas ou versões diferentes
SELECT 'VERIFICANDO DUPLICATAS' as step;

SELECT 
    id,
    title,
    author,
    description,
    pdf_key,
    created_at,
    ROW_NUMBER() OVER (PARTITION BY LOWER(TRIM(title)) ORDER BY created_at DESC) as version_number
FROM books 
WHERE LOWER(author) LIKE '%martin luther king%'
   OR LOWER(title) LIKE '%martin luther%'
   OR LOWER(title) LIKE '%apelo%'
ORDER BY LOWER(TRIM(title)), created_at DESC;

-- STEP 7: Resumo do diagnóstico
SELECT 'RESUMO DO DIAGNÓSTICO' as step;

WITH book_info AS (
    SELECT 
        b.id,
        b.title,
        b.author,
        b.pdf_key,
        CASE WHEN bc.content IS NOT NULL THEN 1 ELSE 0 END as has_content,
        CASE WHEN pf.file_data IS NOT NULL THEN 1 ELSE 0 END as has_pdf,
        CASE WHEN b.pdf_key IS NOT NULL AND b.pdf_key != '' THEN 1 ELSE 0 END as has_pdf_key
    FROM books b
    LEFT JOIN book_contents bc ON bc.book_id = b.id
    LEFT JOIN pdf_files pf ON pf.book_id = b.id::text
    WHERE LOWER(b.title) LIKE '%apelo%consciência%' 
       OR LOWER(b.title) LIKE '%apelo%consciencia%'
       OR (LOWER(b.title) LIKE '%martin luther king%' AND LOWER(b.title) LIKE '%discursos%')
       OR (LOWER(b.author) LIKE '%martin luther king%' AND LOWER(b.title) LIKE '%melhores%')
)
SELECT 
    id,
    title,
    CASE 
        WHEN has_content = 1 THEN '✅ Tem conteúdo estruturado'
        ELSE '❌ Sem conteúdo estruturado'
    END as content_status,
    CASE 
        WHEN has_pdf = 1 THEN '✅ Tem PDF no banco'
        ELSE '❌ Sem PDF no banco'
    END as pdf_status,
    CASE 
        WHEN has_pdf_key = 1 THEN '✅ Tem PDF_KEY'
        ELSE '❌ Sem PDF_KEY'
    END as pdf_key_status,
    CASE 
        WHEN has_content = 1 OR has_pdf = 1 OR has_pdf_key = 1 THEN '✅ PODE SER CORRIGIDO'
        ELSE '❌ PRECISA DE CONTEÚDO'
    END as fix_status
FROM book_info;

-- Mensagem final
SELECT 
    'DIAGNÓSTICO COMPLETO' as status,
    'Verifique os resultados acima para identificar o problema específico' as message;
