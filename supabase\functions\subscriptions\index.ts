import { createClient } from 'npm:@supabase/supabase-js@2'
import Stripe from 'npm:stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    const url = new URL(req.url)
    const pathSegments = url.pathname.split('/').filter(Boolean)
    const apiPath = pathSegments.slice(3).join('/')

    switch (req.method) {
      case 'GET':
        return await handleGetSubscription(supabaseClient, apiPath, req)
      case 'POST':
        return await handleCreateSubscription(supabaseClient, apiPath, req)
      case 'PUT':
        return await handleUpdateSubscription(supabaseClient, apiPath, req)
      case 'DELETE':
        return await handleCancelSubscription(supabaseClient, apiPath, req)
      default:
        return new Response(
          JSON.stringify({ error: 'Method not allowed' }),
          { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Subscription API Error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function handleGetSubscription(supabaseClient: any, path: string, req: Request) {
  const authHeader = req.headers.get('Authorization')
  const token = authHeader?.replace('Bearer ', '')
  
  const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
  
  if (authError || !user) {
    return new Response(
      JSON.stringify({ error: 'Authentication required' }),
      { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  if (path === '' || path === 'current') {
    // GET /subscriptions/current - Get current user subscription
    const { data: subscription, error } = await supabaseClient
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (error) {
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Calculate remaining free access
    let remainingFreeAccess = 0
    if (subscription.subscription_type === 'free') {
      const isInFirstMonth = new Date(subscription.started_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      
      if (isInFirstMonth) {
        remainingFreeAccess = -1 // Unlimited during first month
      } else {
        // Count this month's free accesses
        const currentMonth = new Date().toISOString().slice(0, 7)
        const { data: accessCount } = await supabaseClient
          .from('user_summary_access')
          .select('id', { count: 'exact' })
          .eq('user_id', user.id)
          .gte('accessed_at', `${currentMonth}-01`)
          .lt('accessed_at', `${currentMonth}-32`)

        remainingFreeAccess = Math.max(0, 2 - (accessCount?.length || 0))
      }
    }

    return new Response(
      JSON.stringify({
        data: {
          ...subscription,
          remaining_free_access: remainingFreeAccess
        }
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ error: 'Invalid endpoint' }),
    { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleCreateSubscription(supabaseClient: any, path: string, req: Request) {
  const authHeader = req.headers.get('Authorization')
  const token = authHeader?.replace('Bearer ', '')
  
  const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
  
  if (authError || !user) {
    return new Response(
      JSON.stringify({ error: 'Authentication required' }),
      { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  if (path === 'create-checkout-session') {
    // POST /subscriptions/create-checkout-session - Create Stripe checkout session
    const body = await req.json()
    const { price_id, success_url, cancel_url } = body

    if (!price_id) {
      return new Response(
        JSON.stringify({ error: 'price_id is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get or create Stripe customer
    let customerId: string
    const { data: subscription } = await supabaseClient
      .from('user_subscriptions')
      .select('stripe_customer_id')
      .eq('user_id', user.id)
      .single()

    if (subscription?.stripe_customer_id) {
      customerId = subscription.stripe_customer_id
    } else {
      const customer = await stripe.customers.create({
        email: user.email,
        metadata: {
          supabase_user_id: user.id,
        },
      })
      customerId = customer.id

      // Update subscription with customer ID
      await supabaseClient
        .from('user_subscriptions')
        .update({ stripe_customer_id: customerId })
        .eq('user_id', user.id)
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: price_id,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: success_url || `${req.headers.get('origin')}/dashboard?success=true`,
      cancel_url: cancel_url || `${req.headers.get('origin')}/pricing?canceled=true`,
      metadata: {
        supabase_user_id: user.id,
      },
    })

    return new Response(
      JSON.stringify({ data: { checkout_url: session.url } }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ error: 'Invalid endpoint' }),
    { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleUpdateSubscription(supabaseClient: any, path: string, req: Request) {
  // Handle Stripe webhooks for subscription updates
  if (path === 'webhook') {
    const signature = req.headers.get('stripe-signature')
    const body = await req.text()

    if (!signature) {
      return new Response(
        JSON.stringify({ error: 'Missing stripe-signature header' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        Deno.env.get('STRIPE_WEBHOOK_SECRET') || ''
      )
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return new Response(
        JSON.stringify({ error: 'Invalid signature' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session
        await handleCheckoutCompleted(supabaseClient, session)
        break

      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        const subscription = event.data.object as Stripe.Subscription
        await handleSubscriptionChange(supabaseClient, subscription)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(
      JSON.stringify({ received: true }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ error: 'Invalid endpoint' }),
    { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleCancelSubscription(supabaseClient: any, path: string, req: Request) {
  const authHeader = req.headers.get('Authorization')
  const token = authHeader?.replace('Bearer ', '')
  
  const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
  
  if (authError || !user) {
    return new Response(
      JSON.stringify({ error: 'Authentication required' }),
      { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  if (path === 'cancel') {
    // DELETE /subscriptions/cancel - Cancel current subscription
    const { data: subscription } = await supabaseClient
      .from('user_subscriptions')
      .select('stripe_subscription_id')
      .eq('user_id', user.id)
      .single()

    if (!subscription?.stripe_subscription_id) {
      return new Response(
        JSON.stringify({ error: 'No active subscription found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Cancel subscription in Stripe
    await stripe.subscriptions.update(subscription.stripe_subscription_id, {
      cancel_at_period_end: true,
    })

    // Update local subscription status
    const { data: updatedSubscription, error } = await supabaseClient
      .from('user_subscriptions')
      .update({
        status: 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ data: updatedSubscription }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ error: 'Invalid endpoint' }),
    { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleCheckoutCompleted(supabaseClient: any, session: Stripe.Checkout.Session) {
  const userId = session.metadata?.supabase_user_id
  if (!userId) return

  const subscription = await stripe.subscriptions.retrieve(session.subscription as string)

  await supabaseClient
    .from('user_subscriptions')
    .update({
      subscription_type: 'premium',
      status: 'active',
      stripe_subscription_id: subscription.id,
      expires_at: new Date(subscription.current_period_end * 1000).toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)
}

async function handleSubscriptionChange(supabaseClient: any, subscription: Stripe.Subscription) {
  const customer = await stripe.customers.retrieve(subscription.customer as string)
  const userId = (customer as Stripe.Customer).metadata?.supabase_user_id
  
  if (!userId) return

  const status = subscription.status === 'active' ? 'active' : 
                subscription.status === 'canceled' ? 'cancelled' : 'expired'

  const subscriptionType = status === 'active' ? 'premium' : 'free'

  await supabaseClient
    .from('user_subscriptions')
    .update({
      subscription_type: subscriptionType,
      status: status,
      expires_at: new Date(subscription.current_period_end * 1000).toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)
}