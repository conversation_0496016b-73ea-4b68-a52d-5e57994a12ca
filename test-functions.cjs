const https = require('https');

console.log('🧪 TESTANDO EDGE FUNCTIONS');
console.log('==========================\n');

// Configurações
const SUPABASE_URL = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';

// Função para fazer requisições HTTP
function makeRequest(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname,
      method: method,
      headers: {
        'Authorization': `Bearer ${ANON_KEY}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Node.js Test Client'
      }
    };

    if (data && method === 'POST') {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: responseData
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data && method === 'POST') {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Testar stripe-checkout function
async function testStripeCheckout() {
  console.log('1. 🧪 Testando stripe-checkout function...');
  
  try {
    const testData = {
      price_id: 'price_1Rov58QXmlk54RXcFlW7ymIL',
      user_id: 'test-user-123',
      success_url: 'http://localhost:5173/subscription/success',
      cancel_url: 'http://localhost:5173/subscription/cancel',
      plan_id: 'premium'
    };

    const response = await makeRequest(
      `${SUPABASE_URL}/functions/v1/stripe-checkout`,
      'POST',
      testData
    );

    console.log(`   Status: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      console.log('   ✅ stripe-checkout function está funcionando!');
      const result = JSON.parse(response.body);
      console.log(`   Session ID: ${result.session_id || 'N/A'}`);
    } else if (response.statusCode === 404) {
      console.log('   ❌ stripe-checkout function NÃO foi deployada');
    } else {
      console.log(`   ⚠️  stripe-checkout retornou status ${response.statusCode}`);
      console.log(`   Response: ${response.body}`);
    }
  } catch (error) {
    console.log('   ❌ Erro ao testar stripe-checkout:', error.message);
  }
}

// Testar stripe-webhook function
async function testStripeWebhook() {
  console.log('\n2. 🧪 Testando stripe-webhook function...');
  
  try {
    // Webhook precisa de POST com signature, então vamos testar se existe
    const response = await makeRequest(
      `${SUPABASE_URL}/functions/v1/stripe-webhook`,
      'POST',
      { test: 'data' }
    );

    console.log(`   Status: ${response.statusCode}`);
    
    if (response.statusCode === 400) {
      console.log('   ✅ stripe-webhook function está funcionando!');
      console.log('   (Status 400 é esperado sem signature válida)');
    } else if (response.statusCode === 404) {
      console.log('   ❌ stripe-webhook function NÃO foi deployada');
    } else {
      console.log(`   ⚠️  stripe-webhook retornou status ${response.statusCode}`);
      console.log(`   Response: ${response.body}`);
    }
  } catch (error) {
    console.log('   ❌ Erro ao testar stripe-webhook:', error.message);
  }
}

// Testar stripe-portal function
async function testStripePortal() {
  console.log('\n3. 🧪 Testando stripe-portal function...');
  
  try {
    const testData = {
      customer_id: 'cus_test_customer',
      return_url: 'http://localhost:5173/subscription'
    };

    const response = await makeRequest(
      `${SUPABASE_URL}/functions/v1/stripe-portal`,
      'POST',
      testData
    );

    console.log(`   Status: ${response.statusCode}`);
    
    if (response.statusCode === 200 || response.statusCode === 500) {
      console.log('   ✅ stripe-portal function está funcionando!');
      if (response.statusCode === 500) {
        console.log('   (Status 500 é esperado com customer_id inválido)');
      }
    } else if (response.statusCode === 404) {
      console.log('   ❌ stripe-portal function NÃO foi deployada');
    } else {
      console.log(`   ⚠️  stripe-portal retornou status ${response.statusCode}`);
      console.log(`   Response: ${response.body}`);
    }
  } catch (error) {
    console.log('   ❌ Erro ao testar stripe-portal:', error.message);
  }
}

// Verificar webhook do Stripe
async function checkStripeWebhook() {
  console.log('\n4. 🔍 Verificando webhook do Stripe...');
  
  const Stripe = require('stripe');
  const stripe = new Stripe('sk_test_51RIcDWQXmlk54RXco22WsOUYY2jtE27RhFyvbaaiInFpIDD5yFcbQNPjSilbv6pzKxq1YuihBgAy4ria1OV3l2mY00gw9ED5fy');
  
  try {
    const webhooks = await stripe.webhookEndpoints.list();
    const ourWebhook = webhooks.data.find(w => 
      w.url === `${SUPABASE_URL}/functions/v1/stripe-webhook`
    );
    
    if (ourWebhook) {
      console.log('   ✅ Webhook configurado no Stripe!');
      console.log(`   ID: ${ourWebhook.id}`);
      console.log(`   Status: ${ourWebhook.status}`);
      console.log(`   Eventos: ${ourWebhook.enabled_events.length}`);
    } else {
      console.log('   ❌ Webhook NÃO encontrado no Stripe');
    }
  } catch (error) {
    console.log('   ❌ Erro ao verificar webhook:', error.message);
  }
}

// Executar todos os testes
async function runAllTests() {
  await testStripeCheckout();
  await testStripeWebhook();
  await testStripePortal();
  await checkStripeWebhook();
  
  console.log('\n🎯 RESUMO:');
  console.log('==========');
  console.log('✅ = Function deployada e funcionando');
  console.log('❌ = Function NÃO deployada');
  console.log('⚠️  = Function deployada mas com problemas');
  
  console.log('\n📋 PRÓXIMOS PASSOS:');
  console.log('Se alguma function mostrou ❌, você precisa fazer deploy:');
  console.log('  npx supabase functions deploy stripe-checkout');
  console.log('  npx supabase functions deploy stripe-webhook');
  console.log('  npx supabase functions deploy stripe-portal');
}

// Executar
runAllTests().catch(console.error);
