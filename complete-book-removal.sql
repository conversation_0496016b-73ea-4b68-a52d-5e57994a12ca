-- COMPLETE BOOK REMOVAL SCRIPT
-- This script ensures the target books are completely removed from all tables
-- Execute this in Supabase Dashboard > SQL Editor

-- STEP 1: Show current status
SELECT 'BEFORE REMOVAL - Current Status' as step;

SELECT 
    'Napoleon Hill books' as type,
    COUNT(*) as count,
    STRING_AGG(CONCAT('ID:', id, ' - "', title, '"'), '; ') as books
FROM books 
WHERE LOWER(author) LIKE '%napoleon hill%'
   OR (LOWER(title) LIKE '%pense e enriqueça%' OR LOWER(title) LIKE '%pense e enriqueca%')
   OR LOWER(title) LIKE '%pense%enriqu%'

UNION ALL

SELECT 
    'Gestalt-Terapia books' as type,
    COUNT(*) as count,
    STRING_AGG(CONCAT('ID:', id, ' - "', title, '"'), '; ') as books
FROM books 
WHERE (LOWER(title) LIKE '%gestalt%' AND <PERSON>OWER(title) LIKE '%terapia%')
   OR (LOWER(title) LIKE '%gestalt%' AND <PERSON>OW<PERSON>(author) LIKE '%perls%');

-- STEP 2: Remove all related data
DO $$
DECLARE
    book_record RECORD;
    total_removed INTEGER := 0;
BEGIN
    RAISE NOTICE 'Starting complete book removal process...';
    
    -- Remove Napoleon Hill books
    FOR book_record IN 
        SELECT id, title, author 
        FROM books 
        WHERE LOWER(author) LIKE '%napoleon hill%'
           OR (LOWER(title) LIKE '%pense e enriqueça%' OR LOWER(title) LIKE '%pense e enriqueca%')
           OR LOWER(title) LIKE '%pense%enriqu%'
    LOOP
        RAISE NOTICE 'Removing Napoleon Hill book: ID=%, Title="%", Author="%"', 
            book_record.id, book_record.title, book_record.author;
            
        -- Remove user progress
        DELETE FROM user_reading_progress WHERE book_id = book_record.id::TEXT;
        
        -- Remove summaries
        DELETE FROM summaries WHERE book_id = book_record.id;
        
        -- Remove book contents
        DELETE FROM book_contents WHERE book_id = book_record.id;
        
        -- Remove PDF files
        DELETE FROM pdf_files WHERE book_id = book_record.id::TEXT;
        
        -- Remove the book itself
        DELETE FROM books WHERE id = book_record.id;
        
        total_removed := total_removed + 1;
    END LOOP;
    
    -- Remove Gestalt-Terapia books
    FOR book_record IN 
        SELECT id, title, author 
        FROM books 
        WHERE (LOWER(title) LIKE '%gestalt%' AND LOWER(title) LIKE '%terapia%')
           OR (LOWER(title) LIKE '%gestalt%' AND LOWER(author) LIKE '%perls%')
    LOOP
        RAISE NOTICE 'Removing Gestalt book: ID=%, Title="%", Author="%"', 
            book_record.id, book_record.title, book_record.author;
            
        -- Remove user progress
        DELETE FROM user_reading_progress WHERE book_id = book_record.id::TEXT;
        
        -- Remove summaries
        DELETE FROM summaries WHERE book_id = book_record.id;
        
        -- Remove book contents
        DELETE FROM book_contents WHERE book_id = book_record.id;
        
        -- Remove PDF files
        DELETE FROM pdf_files WHERE book_id = book_record.id::TEXT;
        
        -- Remove the book itself
        DELETE FROM books WHERE id = book_record.id;
        
        total_removed := total_removed + 1;
    END LOOP;
    
    RAISE NOTICE 'REMOVAL COMPLETE: % books removed successfully', total_removed;
END $$;

-- STEP 3: Clean up any orphaned data
DELETE FROM user_reading_progress 
WHERE book_id NOT IN (SELECT id::TEXT FROM books);

DELETE FROM summaries 
WHERE book_id NOT IN (SELECT id FROM books);

DELETE FROM book_contents 
WHERE book_id NOT IN (SELECT id FROM books);

DELETE FROM pdf_files 
WHERE book_id NOT IN (SELECT id::TEXT FROM books);

-- STEP 4: Final verification
SELECT 'AFTER REMOVAL - Verification' as step;

SELECT 
    'Napoleon Hill books remaining' as type,
    COUNT(*) as count
FROM books 
WHERE LOWER(author) LIKE '%napoleon hill%'
   OR (LOWER(title) LIKE '%pense e enriqueça%' OR LOWER(title) LIKE '%pense e enriqueca%')
   OR LOWER(title) LIKE '%pense%enriqu%'

UNION ALL

SELECT 
    'Gestalt-Terapia books remaining' as type,
    COUNT(*) as count
FROM books 
WHERE (LOWER(title) LIKE '%gestalt%' AND LOWER(title) LIKE '%terapia%')
   OR (LOWER(title) LIKE '%gestalt%' AND LOWER(author) LIKE '%perls%')

UNION ALL

SELECT 
    'Total books in database' as type,
    COUNT(*) as count
FROM books

UNION ALL

SELECT 
    'Orphaned user progress records' as type,
    COUNT(*) as count
FROM user_reading_progress urp
WHERE NOT EXISTS (SELECT 1 FROM books b WHERE b.id::TEXT = urp.book_id)

UNION ALL

SELECT 
    'Orphaned summaries' as type,
    COUNT(*) as count
FROM summaries s
WHERE NOT EXISTS (SELECT 1 FROM books b WHERE b.id = s.book_id)

UNION ALL

SELECT 
    'Orphaned book contents' as type,
    COUNT(*) as count
FROM book_contents bc
WHERE NOT EXISTS (SELECT 1 FROM books b WHERE b.id = bc.book_id);

-- Show sample of remaining books to verify library integrity
SELECT 'Sample of remaining books:' as info;
SELECT id, title, author, category, is_featured 
FROM books 
WHERE is_featured = true
ORDER BY id 
LIMIT 10;

RAISE NOTICE 'Complete book removal process finished. Check verification results above.';
