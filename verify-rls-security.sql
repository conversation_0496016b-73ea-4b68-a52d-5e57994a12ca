-- Verify RLS Security Configuration
-- Execute this SQL in Supabase Dashboard > SQL Editor after running fix-rls-security.sql

-- STEP 1: Check RLS status on all public tables
SELECT 'RLS STATUS CHECK' as verification_step;

SELECT 
  t.table_name,
  CASE 
    WHEN c.relrowsecurity THEN '✅ ENABLED'
    ELSE '❌ DISABLED'
  END as rls_status,
  CASE 
    WHEN c.relrowsecurity THEN 'SECURE'
    ELSE '⚠️ SECURITY RISK'
  END as security_level
FROM information_schema.tables t
LEFT JOIN pg_class c ON c.relname = t.table_name
LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE t.table_schema = 'public'
  AND t.table_type = 'BASE TABLE'
  AND t.table_name NOT LIKE 'pg_%'
  AND t.table_name NOT LIKE 'sql_%'
ORDER BY t.table_name;

-- STEP 2: Check policies on critical tables
SELECT 'POLICIES CHECK' as verification_step;

SELECT 
  tablename,
  COUNT(*) as policy_count,
  STRING_AGG(policyname, ', ') as policies
FROM pg_policies 
WHERE schemaname = 'public'
GROUP BY tablename
ORDER BY tablename;

-- STEP 3: Detailed policy information for book_contents
SELECT 'BOOK_CONTENTS POLICIES DETAIL' as verification_step;

SELECT 
  policyname,
  cmd as operation,
  CASE 
    WHEN cmd = 'SELECT' THEN 'Read Access'
    WHEN cmd = 'INSERT' THEN 'Create Access'
    WHEN cmd = 'UPDATE' THEN 'Update Access'
    WHEN cmd = 'DELETE' THEN 'Delete Access'
    WHEN cmd = 'ALL' THEN 'Full Access'
    ELSE cmd
  END as access_type,
  CASE 
    WHEN qual IS NOT NULL THEN 'Has Conditions'
    ELSE 'No Conditions'
  END as conditions,
  CASE 
    WHEN with_check IS NOT NULL THEN 'Has Check'
    ELSE 'No Check'
  END as check_clause
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'book_contents'
ORDER BY cmd;

-- STEP 4: Test access permissions (simulation)
SELECT 'ACCESS PERMISSIONS TEST' as verification_step;

-- Check if anonymous users can read book_contents
SELECT 
  'book_contents' as table_name,
  'anon' as role,
  CASE 
    WHEN has_table_privilege('anon', 'book_contents', 'SELECT') THEN '✅ CAN READ'
    ELSE '❌ CANNOT READ'
  END as read_access,
  CASE 
    WHEN has_table_privilege('anon', 'book_contents', 'INSERT') THEN '⚠️ CAN WRITE'
    ELSE '✅ CANNOT WRITE'
  END as write_access;

-- Check if authenticated users have proper access
SELECT 
  'book_contents' as table_name,
  'authenticated' as role,
  CASE 
    WHEN has_table_privilege('authenticated', 'book_contents', 'SELECT') THEN '✅ CAN READ'
    ELSE '❌ CANNOT READ'
  END as read_access,
  CASE 
    WHEN has_table_privilege('authenticated', 'book_contents', 'INSERT') THEN '✅ CAN WRITE'
    ELSE '❌ CANNOT WRITE'
  END as write_access;

-- STEP 5: Check for any remaining security issues
SELECT 'SECURITY ISSUES CHECK' as verification_step;

SELECT 
  table_name,
  'RLS not enabled on public table' as issue,
  'HIGH' as severity
FROM information_schema.tables t
LEFT JOIN pg_class c ON c.relname = t.table_name
LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE t.table_schema = 'public'
  AND t.table_type = 'BASE TABLE'
  AND t.table_name NOT LIKE 'pg_%'
  AND t.table_name NOT LIKE 'sql_%'
  AND (c.relrowsecurity IS FALSE OR c.relrowsecurity IS NULL)

UNION ALL

SELECT 
  tablename,
  'No RLS policies defined' as issue,
  'MEDIUM' as severity
FROM pg_tables pt
LEFT JOIN pg_class pc ON pc.relname = pt.tablename
LEFT JOIN pg_policies pp ON pp.tablename = pt.tablename AND pp.schemaname = pt.schemaname
WHERE pt.schemaname = 'public'
  AND pc.relrowsecurity = true
  AND pp.policyname IS NULL
  AND pt.tablename NOT LIKE 'pg_%'
  AND pt.tablename NOT LIKE 'sql_%'

ORDER BY severity DESC, table_name;

-- STEP 6: Summary report
SELECT 'SECURITY SUMMARY REPORT' as verification_step;

WITH rls_stats AS (
  SELECT 
    COUNT(*) as total_tables,
    COUNT(CASE WHEN c.relrowsecurity THEN 1 END) as rls_enabled_tables,
    COUNT(CASE WHEN c.relrowsecurity IS FALSE OR c.relrowsecurity IS NULL THEN 1 END) as rls_disabled_tables
  FROM information_schema.tables t
  LEFT JOIN pg_class c ON c.relname = t.table_name
  LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
  WHERE t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
    AND t.table_name NOT LIKE 'pg_%'
    AND t.table_name NOT LIKE 'sql_%'
),
policy_stats AS (
  SELECT COUNT(DISTINCT tablename) as tables_with_policies
  FROM pg_policies 
  WHERE schemaname = 'public'
)
SELECT 
  r.total_tables,
  r.rls_enabled_tables,
  r.rls_disabled_tables,
  p.tables_with_policies,
  CASE 
    WHEN r.rls_disabled_tables = 0 THEN '✅ ALL TABLES SECURE'
    ELSE '⚠️ ' || r.rls_disabled_tables || ' TABLES NEED RLS'
  END as security_status,
  CASE 
    WHEN r.rls_disabled_tables = 0 AND p.tables_with_policies >= r.rls_enabled_tables THEN '🔒 SECURITY OPTIMAL'
    WHEN r.rls_disabled_tables = 0 THEN '🔐 SECURITY GOOD'
    ELSE '🚨 SECURITY ISSUES FOUND'
  END as overall_rating
FROM rls_stats r, policy_stats p;

-- Final message
SELECT 
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.tables t
      LEFT JOIN pg_class c ON c.relname = t.table_name
      WHERE t.table_schema = 'public'
        AND t.table_type = 'BASE TABLE'
        AND (c.relrowsecurity IS FALSE OR c.relrowsecurity IS NULL)
        AND t.table_name NOT LIKE 'pg_%'
    ) THEN '⚠️ SECURITY ISSUES REMAIN - Check tables above'
    ELSE '✅ ALL SECURITY ISSUES RESOLVED'
  END as final_status;
