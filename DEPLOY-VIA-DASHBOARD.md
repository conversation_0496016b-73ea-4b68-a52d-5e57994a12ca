# 🚀 DEPLOY VIA SUPABASE DASHBOARD

## 🎯 **SITUAÇÃO ATUAL:**

✅ **Código local está CORRETO** (mensagens em inglês)
⚠️ **Function deployada está ANTIGA** (mensagens em português)

## 📋 **OPÇÃO 1 - VIA DASHBOARD (MAIS FÁCIL):**

### **1. Acesse o Supabase Dashboard:**
```
https://supabase.com/dashboard/project/qmeelujsnpbcdkzhcwmm/functions
```

### **2. Encontre a função "stripe-portal"**
- Clique na função existente
- Ou clique em "Create a new function"

### **3. Cole o código atualizado:**

Copie e cole este código completo:

```typescript
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0'
import Stripe from 'https://esm.sh/stripe@14.0.0?target=deno'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
      apiVersion: '2023-10-16',
    })

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    const { customer_id, return_url } = await req.json()

    if (!customer_id) {
      return new Response(
        JSON.stringify({ error: 'Missing customer_id parameter' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create Stripe customer portal session
    const session = await stripe.billingPortal.sessions.create({
      customer: customer_id,
      return_url: return_url || `${Deno.env.get('SUPABASE_URL')}/subscription`,
    })

    return new Response(
      JSON.stringify({ url: session.url }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error creating portal session:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
```

### **4. Salvar e Deploy:**
- Clique em "Save" ou "Deploy"
- Aguarde o deploy completar

---

## 📋 **OPÇÃO 2 - VIA CLI (SE CONSEGUIR LOGIN):**

### **1. Fazer login:**
```bash
npx supabase login
```

### **2. Linkar projeto:**
```bash
npx supabase link --project-ref qmeelujsnpbcdkzhcwmm
```

### **3. Deploy:**
```bash
npx supabase functions deploy stripe-portal
```

---

## 🧪 **VERIFICAR SE FUNCIONOU:**

Após o deploy, execute:
```bash
node test-portal-detailed.cjs
```

**Resultado esperado:**
- ✅ "Missing customer_id parameter" (inglês)
- ❌ "Customer ID é obrigatório" (português)

---

## 🔐 **SECRETS NECESSÁRIOS:**

Certifique-se de que estes secrets estão configurados no Supabase:

```
STRIPE_SECRET_KEY=sk_test_51RIcDWQXmlk54RXco22WsOUYY2jtE27RhFyvbaaiInFpIDD5yFcbQNPjSilbv6pzKxq1YuihBgAy4ria1OV3l2mY00gw9ED5fy
STRIPE_WEBHOOK_SECRET=whsec_owwQcmHY0v5z2c4JyIUMN2dWdKzooVcaY
SUPABASE_URL=https://qmeelujsnpbcdkzhcwmm.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjM2MjM0NSwiZXhwIjoyMDYxOTM4MzQ1fQ.zDusZDEOpQjEjgZhydao_ShbqddElyIA-rExxBj8yCM
```

---

## 🎉 **APÓS O DEPLOY:**

**Sua integração estará 100% funcional:**

✅ **stripe-checkout** - Funcionando
✅ **stripe-webhook** - Funcionando  
✅ **stripe-portal** - Funcionando (versão correta)
✅ **Webhook Stripe** - Configurado com 24 eventos

**URLs das Functions:**
- `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-checkout`
- `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-webhook`
- `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal`

---

## 🚀 **RECOMENDAÇÃO:**

**Use a OPÇÃO 1 (Dashboard)** - é mais fácil e não depende do CLI funcionando.

Acesse: https://supabase.com/dashboard/project/qmeelujsnpbcdkzhcwmm/functions
