-- Limpeza Ultra Simples da Biblioteca - Sem CTEs complexos
-- Execute este SQL no Supabase Dashboard > SQL Editor

-- STEP 1: Mostrar livros que serão removidos
SELECT 'LIVROS QUE SERÃO REMOVIDOS' as step;

SELECT 
    b.id,
    b.title,
    b.author,
    'SERÁ REMOVIDO' as status
FROM books b
LEFT JOIN book_contents bc ON bc.book_id = b.id
WHERE (
    -- Livros específicos mencionados
    (LOWER(b.title) LIKE '%fundamentos da psicologia analítica%' AND LOWER(b.author) LIKE '%carl gustav jung%')
    OR (LOWER(b.title) LIKE '%freud%' AND LOWER(b.title) LIKE '%fundamentos%')
    -- Ou livros com conteúdo placeholder
    OR bc.content::text LIKE '%conteúdo completo está sendo processado%'
    OR bc.content::text LIKE '%Nossa equipe está trabalhando%'
);

-- STEP 2: Traduzir título do livro "Determined"
UPDATE books 
SET 
    title = 'Determinado: Uma Ciência da Vida Sem Livre Arbítrio',
    description = 'Investigação científica sobre livre arbítrio e determinismo comportamental'
WHERE LOWER(title) LIKE '%determined%science%life%free%will%'
   OR (LOWER(author) LIKE '%robert sapolsky%' AND LOWER(title) LIKE '%determined%');

-- STEP 3: Remover progresso dos usuários para livros específicos
DELETE FROM user_reading_progress 
WHERE book_id IN (
    SELECT b.id::text
    FROM books b
    WHERE (LOWER(b.title) LIKE '%fundamentos da psicologia analítica%' AND LOWER(b.author) LIKE '%carl gustav jung%')
       OR (LOWER(b.title) LIKE '%freud%' AND LOWER(b.title) LIKE '%fundamentos%')
);

-- STEP 4: Remover conteúdo dos livros específicos
DELETE FROM book_contents 
WHERE book_id IN (
    SELECT b.id
    FROM books b
    WHERE (LOWER(b.title) LIKE '%fundamentos da psicologia analítica%' AND LOWER(b.author) LIKE '%carl gustav jung%')
       OR (LOWER(b.title) LIKE '%freud%' AND LOWER(b.title) LIKE '%fundamentos%')
);

-- STEP 5: Remover os livros específicos
DELETE FROM books 
WHERE (LOWER(title) LIKE '%fundamentos da psicologia analítica%' AND LOWER(author) LIKE '%carl gustav jung%')
   OR (LOWER(title) LIKE '%freud%' AND LOWER(title) LIKE '%fundamentos%');

-- STEP 6: Remover progresso para livros com placeholder
DELETE FROM user_reading_progress 
WHERE book_id IN (
    SELECT b.id::text
    FROM books b
    JOIN book_contents bc ON bc.book_id = b.id
    WHERE bc.content::text LIKE '%conteúdo completo está sendo processado%'
       OR bc.content::text LIKE '%Nossa equipe está trabalhando%'
       OR bc.content::text LIKE '%Aguarde o processamento completo%'
);

-- STEP 7: Remover conteúdo placeholder
DELETE FROM book_contents 
WHERE content::text LIKE '%conteúdo completo está sendo processado%'
   OR content::text LIKE '%Nossa equipe está trabalhando%'
   OR content::text LIKE '%Aguarde o processamento completo%';

-- STEP 8: Remover livros órfãos (sem conteúdo nem PDF) - método simples
DELETE FROM user_reading_progress 
WHERE book_id IN (
    SELECT b.id::text
    FROM books b
    LEFT JOIN book_contents bc ON bc.book_id = b.id
    WHERE bc.content IS NULL 
      AND (b.pdf_key IS NULL OR b.pdf_key = '')
      AND b.title NOT LIKE '%Hábitos Atômicos%'
      AND b.title NOT LIKE '%Pai Rico%'
      AND b.title NOT LIKE '%Investidor Inteligente%'
      AND b.title NOT LIKE '%Cem Anos%'
      AND b.title NOT LIKE '%Determinado%'
);

DELETE FROM books 
WHERE id IN (
    SELECT b.id
    FROM books b
    LEFT JOIN book_contents bc ON bc.book_id = b.id
    WHERE bc.content IS NULL 
      AND (b.pdf_key IS NULL OR b.pdf_key = '')
      AND b.title NOT LIKE '%Hábitos Atômicos%'
      AND b.title NOT LIKE '%Pai Rico%'
      AND b.title NOT LIKE '%Investidor Inteligente%'
      AND b.title NOT LIKE '%Cem Anos%'
      AND b.title NOT LIKE '%Determinado%'
);

-- STEP 9: Remover duplicatas simples - método direto
-- Primeiro, identificar duplicatas por título similar
CREATE TEMP TABLE temp_duplicates AS
SELECT 
    id,
    title,
    pdf_key,
    is_featured,
    ROW_NUMBER() OVER (
        PARTITION BY LOWER(TRIM(title))
        ORDER BY 
            CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 ELSE 2 END,
            CASE WHEN is_featured THEN 1 ELSE 2 END,
            id DESC
    ) as rn
FROM books;

-- Remover progresso das duplicatas
DELETE FROM user_reading_progress 
WHERE book_id IN (
    SELECT id::text FROM temp_duplicates WHERE rn > 1
);

-- Remover conteúdo das duplicatas
DELETE FROM book_contents 
WHERE book_id IN (
    SELECT id FROM temp_duplicates WHERE rn > 1
);

-- Remover as duplicatas
DELETE FROM books 
WHERE id IN (
    SELECT id FROM temp_duplicates WHERE rn > 1
);

-- Limpar tabela temporária
DROP TABLE temp_duplicates;

-- STEP 10: Garantir que livros essenciais estejam em destaque
UPDATE books 
SET is_featured = true
WHERE LOWER(title) LIKE '%hábitos atômicos%'
   OR LOWER(title) LIKE '%pai rico%pai pobre%'
   OR LOWER(title) LIKE '%cem anos%solidão%'
   OR LOWER(title) LIKE '%determinado%ciência%vida%';

-- STEP 11: Limitar livros em destaque - método simples
-- Primeiro desmarcar todos
UPDATE books SET is_featured = false;

-- Depois marcar apenas os melhores
UPDATE books 
SET is_featured = true
WHERE id IN (
    SELECT id FROM (
        SELECT 
            id,
            ROW_NUMBER() OVER (
                ORDER BY 
                    CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 ELSE 2 END,
                    CASE WHEN LOWER(title) LIKE '%hábitos atômicos%' THEN 1 ELSE 2 END,
                    CASE WHEN LOWER(title) LIKE '%pai rico%' THEN 1 ELSE 2 END,
                    CASE WHEN LOWER(title) LIKE '%cem anos%' THEN 1 ELSE 2 END,
                    CASE WHEN LOWER(title) LIKE '%determinado%' THEN 1 ELSE 2 END,
                    id DESC
            ) as rn
        FROM books
    ) ranked 
    WHERE rn <= 12
);

-- STEP 12: Verificação final
SELECT 'RESULTADO FINAL' as step;

SELECT 
    'Total de livros' as metric,
    COUNT(*) as count
FROM books

UNION ALL

SELECT 
    'Livros em destaque' as metric,
    COUNT(*) as count
FROM books 
WHERE is_featured = true

UNION ALL

SELECT 
    'Livros com PDF' as metric,
    COUNT(*) as count
FROM books 
WHERE pdf_key IS NOT NULL AND pdf_key != '';

-- STEP 13: Mostrar livros em destaque
SELECT 'LIVROS EM DESTAQUE APÓS LIMPEZA' as step;

SELECT 
    id,
    title,
    author,
    CASE 
        WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN '✅ PDF'
        WHEN EXISTS (SELECT 1 FROM book_contents bc WHERE bc.book_id = books.id) THEN '📄 CONTEÚDO'
        ELSE '❌ VAZIO'
    END as status
FROM books 
WHERE is_featured = true
ORDER BY title;

-- STEP 14: Verificar se a tradução funcionou
SELECT 'VERIFICAÇÃO DA TRADUÇÃO' as step;

SELECT 
    id,
    title,
    author
FROM books 
WHERE LOWER(title) LIKE '%determinado%'
   OR LOWER(author) LIKE '%robert sapolsky%';

-- STEP 15: Verificar se livros problemáticos foram removidos
SELECT 'VERIFICAÇÃO DE REMOÇÃO' as step;

SELECT 
    COUNT(*) as livros_com_placeholder_restantes
FROM books b
LEFT JOIN book_contents bc ON bc.book_id = b.id
WHERE bc.content::text LIKE '%conteúdo completo está sendo processado%'
   OR bc.content::text LIKE '%Nossa equipe está trabalhando%'
   OR (LOWER(b.title) LIKE '%fundamentos da psicologia analítica%' AND LOWER(b.author) LIKE '%carl gustav jung%');

-- Mensagem final
SELECT 
    '✅ LIMPEZA ULTRA SIMPLES CONCLUÍDA' as status,
    'Todos os problemas foram resolvidos' as message;
