import { createClient } from 'npm:@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

interface Database {
  public: {
    Tables: {
      summaries: {
        Row: {
          id: string
          book_id: string
          title: string
          content: string
          key_points: any[]
          word_count: number
          estimated_reading_time: number
          is_published: boolean
          created_at: string
          updated_at: string
        }
      }
      books: {
        Row: {
          id: string
          title: string
          author: string
          category_id: string
          description: string
          cover_image_url: string
          reading_time_minutes: number
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          slug: string
        }
      }
    }
  }
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const url = new URL(req.url)
    const pathSegments = url.pathname.split('/').filter(Boolean)
    
    // Remove 'functions/v1/summaries' from path
    const apiPath = pathSegments.slice(3).join('/')
    
    switch (req.method) {
      case 'GET':
        return await handleGet(supabaseClient, apiPath, url.searchParams, req)
      case 'POST':
        return await handlePost(supabaseClient, apiPath, req)
      case 'PUT':
        return await handlePut(supabaseClient, apiPath, req)
      case 'DELETE':
        return await handleDelete(supabaseClient, apiPath, req)
      default:
        return new Response(
          JSON.stringify({ error: 'Method not allowed' }),
          { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('API Error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function handleGet(supabaseClient: any, path: string, searchParams: URLSearchParams, req: Request) {
  const authHeader = req.headers.get('Authorization')
  const token = authHeader?.replace('Bearer ', '')
  
  // Get user from token
  const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
  
  if (path === '' || path === 'list') {
    // GET /summaries - List all available summaries
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const offset = (page - 1) * limit

    let query = supabaseClient
      .from('summaries')
      .select(`
        *,
        books (
          id,
          title,
          author,
          cover_image_url,
          reading_time_minutes,
          categories (
            id,
            name,
            slug
          )
        )
      `)
      .eq('is_published', true)
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false })

    if (category) {
      query = query.eq('books.categories.slug', category)
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,books.title.ilike.%${search}%,books.author.ilike.%${search}%`)
    }

    const { data: summaries, error } = await query

    if (error) {
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check access for each summary if user is authenticated
    let summariesWithAccess = summaries
    if (user) {
      summariesWithAccess = await Promise.all(
        summaries.map(async (summary: any) => {
          const { data: hasAccess } = await supabaseClient
            .rpc('user_has_summary_access', {
              user_uuid: user.id,
              summary_uuid: summary.id
            })

          return {
            ...summary,
            has_access: hasAccess,
            content: hasAccess ? summary.content : null
          }
        })
      )
    } else {
      // For unauthenticated users, show only basic info
      summariesWithAccess = summaries.map((summary: any) => ({
        ...summary,
        has_access: false,
        content: null
      }))
    }

    return new Response(
      JSON.stringify({
        data: summariesWithAccess,
        pagination: {
          page,
          limit,
          total: summaries.length
        }
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  if (path.startsWith('categories')) {
    // GET /summaries/categories - List all categories
    const { data: categories, error } = await supabaseClient
      .from('categories')
      .select('*')
      .order('name')

    if (error) {
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ data: categories }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  if (path.startsWith('free-monthly')) {
    // GET /summaries/free-monthly - Get current month's free summaries
    const currentMonth = new Date().toISOString().slice(0, 7) // YYYY-MM format

    const { data: freeSummaries, error } = await supabaseClient
      .from('monthly_free_summaries')
      .select(`
        *,
        summaries (
          *,
          books (
            id,
            title,
            author,
            cover_image_url,
            reading_time_minutes,
            categories (
              id,
              name,
              slug
            )
          )
        )
      `)
      .eq('month_year', currentMonth)
      .order('selection_order')

    if (error) {
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ data: freeSummaries }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  // GET /summaries/:id - Get specific summary
  const summaryId = path
  if (!summaryId) {
    return new Response(
      JSON.stringify({ error: 'Summary ID is required' }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  if (!user) {
    return new Response(
      JSON.stringify({ error: 'Authentication required' }),
      { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  // Check if user has access to this summary
  const { data: hasAccess, error: accessError } = await supabaseClient
    .rpc('user_has_summary_access', {
      user_uuid: user.id,
      summary_uuid: summaryId
    })

  if (accessError) {
    return new Response(
      JSON.stringify({ error: accessError.message }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  if (!hasAccess) {
    return new Response(
      JSON.stringify({ error: 'Access denied. Upgrade to premium or wait for free monthly selection.' }),
      { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  // Get the summary with full content
  const { data: summary, error } = await supabaseClient
    .from('summaries')
    .select(`
      *,
      books (
        id,
        title,
        author,
        cover_image_url,
        reading_time_minutes,
        categories (
          id,
          name,
          slug
        )
      )
    `)
    .eq('id', summaryId)
    .eq('is_published', true)
    .single()

  if (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  // Record access
  await supabaseClient
    .from('user_summary_access')
    .upsert({
      user_id: user.id,
      summary_id: summaryId,
      access_type: hasAccess === 'premium' ? 'premium' : 'free'
    })

  return new Response(
    JSON.stringify({ data: summary }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handlePost(supabaseClient: any, path: string, req: Request) {
  // Admin only endpoints for creating summaries
  return new Response(
    JSON.stringify({ error: 'Not implemented' }),
    { status: 501, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handlePut(supabaseClient: any, path: string, req: Request) {
  // Admin only endpoints for updating summaries
  return new Response(
    JSON.stringify({ error: 'Not implemented' }),
    { status: 501, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleDelete(supabaseClient: any, path: string, req: Request) {
  // Admin only endpoints for deleting summaries
  return new Response(
    JSON.stringify({ error: 'Not implemented' }),
    { status: 501, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}