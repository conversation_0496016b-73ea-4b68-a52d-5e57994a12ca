import { useState, useEffect, useCallback, useRef } from 'react'
import { DashboardData, dataService } from '../lib/dataService'
import { DashboardUpdate } from '../lib/realtimeManager'
import { optimisticUpdateManager, OptimisticUpdate } from '../lib/optimisticUpdates'

interface UseRealtimeDashboardReturn {
  dashboardData: DashboardData | null
  loading: boolean
  error: string | null
  refreshDashboard: () => Promise<void>
  pendingUpdates: OptimisticUpdate[]
}

export function useRealtimeDashboard(): UseRealtimeDashboardReturn {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pendingUpdates, setPendingUpdates] = useState<OptimisticUpdate[]>([])
  
  const realtimeUnsubscribe = useRef<(() => void) | null>(null)
  const optimisticUnsubscribe = useRef<(() => void) | null>(null)

  // Load initial dashboard data
  const loadDashboardData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('🔄 Loading dashboard data...')
      
      const data = await dataService.getDashboardData()
      console.log('✅ Dashboard data loaded:', data)
      
      setDashboardData(data)
    } catch (err) {
      console.error('❌ Error loading dashboard data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }, [])

  // Handle real-time updates
  const handleRealtimeUpdate = useCallback((update: DashboardUpdate) => {
    console.log('📡 Received real-time update:', update)
    
    setDashboardData(currentData => {
      if (!currentData) return currentData

      // Apply the real-time update to current data
      switch (update.type) {
        case 'favorites':
        case 'progress':
          // For favorites and progress, we need to refresh the entire dashboard
          // to ensure proper categorization and book data joining
          loadDashboardData()
          return currentData

        case 'preferences':
          return {
            ...currentData,
            preferences: {
              ...currentData.preferences,
              ...update.data
            }
          }

        case 'subscription':
          return {
            ...currentData,
            subscription: {
              ...currentData.subscription,
              ...update.data
            }
          }

        case 'statistics':
          return {
            ...currentData,
            streak: {
              ...currentData.streak,
              ...update.data
            }
          }

        default:
          return currentData
      }
    })
  }, [loadDashboardData])

  // Handle optimistic updates
  const handleOptimisticUpdates = useCallback((updates: OptimisticUpdate[]) => {
    console.log('⚡ Optimistic updates changed:', updates.length)
    setPendingUpdates(updates)
    
    // Apply optimistic updates to dashboard data
    setDashboardData(currentData => {
      if (!currentData) return currentData
      return optimisticUpdateManager.applyToDashboardData(currentData)
    })
  }, [])

  // Setup subscriptions
  useEffect(() => {
    console.log('🔄 Setting up real-time dashboard subscriptions')

    // Subscribe to real-time updates
    realtimeUnsubscribe.current = dataService.subscribeToRealtimeUpdates(handleRealtimeUpdate)

    // Subscribe to optimistic updates
    optimisticUnsubscribe.current = optimisticUpdateManager.subscribe(handleOptimisticUpdates)

    // Load initial data
    loadDashboardData()

    // Cleanup function
    return () => {
      console.log('🧹 Cleaning up real-time dashboard subscriptions')
      
      if (realtimeUnsubscribe.current) {
        realtimeUnsubscribe.current()
        realtimeUnsubscribe.current = null
      }
      
      if (optimisticUnsubscribe.current) {
        optimisticUnsubscribe.current()
        optimisticUnsubscribe.current = null
      }
      
      dataService.cleanupRealtimeSubscriptions()
    }
  }, [handleRealtimeUpdate, handleOptimisticUpdates, loadDashboardData])

  // Refresh function
  const refreshDashboard = useCallback(async () => {
    await loadDashboardData()
  }, [loadDashboardData])

  return {
    dashboardData,
    loading,
    error,
    refreshDashboard,
    pendingUpdates
  }
}

// Hook for individual dashboard sections
export function useRealtimeDashboardSection<T>(
  selector: (data: DashboardData) => T,
  dependencies: any[] = []
): {
  data: T | null
  loading: boolean
  error: string | null
} {
  const { dashboardData, loading, error } = useRealtimeDashboard()
  
  const sectionData = dashboardData ? selector(dashboardData) : null
  
  return {
    data: sectionData,
    loading,
    error
  }
}

// Specific hooks for common sections
export function useRealtimeFavorites() {
  return useRealtimeDashboardSection(data => data.favorites)
}

export function useRealtimeProgress() {
  return useRealtimeDashboardSection(data => data.in_progress)
}

export function useRealtimeStatistics() {
  return useRealtimeDashboardSection(data => data.stats)
}

export function useRealtimePreferences() {
  return useRealtimeDashboardSection(data => data.preferences)
}

export function useRealtimeSubscription() {
  return useRealtimeDashboardSection(data => data.subscription)
}
