import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Loader2, Crown, Check, X } from 'lucide-react'
import { SUBSCRIPTION_PLANS, stripeService, formatPrice, SubscriptionPlan } from '../../lib/stripe'
import { useRealtimeSubscription } from '../../hooks/useRealtimeDashboard'
import { dataService } from '../../lib/dataService'

interface SubscriptionManagerProps {
  userId: string
  currentPlan?: SubscriptionPlan
  onPlanChange?: (plan: SubscriptionPlan) => void
}

export function SubscriptionManager({ 
  userId, 
  currentPlan = 'FREE',
  onPlanChange 
}: SubscriptionManagerProps) {
  const [loading, setLoading] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  
  const { data: subscription } = useRealtimeSubscription()

  const handleUpgrade = async (planId: SubscriptionPlan) => {
    if (planId === 'FREE') return

    try {
      setLoading(planId)
      setError(null)

      const successUrl = `${window.location.origin}/subscription/success`
      const cancelUrl = `${window.location.origin}/subscription/cancel`

      const { sessionId } = await stripeService.createCheckoutSession(
        planId,
        userId,
        successUrl,
        cancelUrl
      )

      await stripeService.redirectToCheckout(sessionId)
    } catch (err) {
      console.error('Error upgrading subscription:', err)
      setError(err instanceof Error ? err.message : 'Failed to upgrade subscription')
    } finally {
      setLoading(null)
    }
  }

  const handleManageSubscription = async () => {
    if (!subscription?.stripe_customer_id) {
      setError('No customer ID found')
      return
    }

    try {
      setLoading('manage')
      const returnUrl = window.location.origin
      
      const { url } = await stripeService.createPortalSession(
        subscription.stripe_customer_id,
        returnUrl
      )

      window.location.href = url
    } catch (err) {
      console.error('Error opening customer portal:', err)
      setError(err instanceof Error ? err.message : 'Failed to open subscription management')
    } finally {
      setLoading(null)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Ativo</Badge>
      case 'past_due':
        return <Badge className="bg-yellow-100 text-yellow-800">Pagamento Pendente</Badge>
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800">Cancelado</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">Gratuito</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Current Subscription Status */}
      {subscription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Sua Assinatura</span>
              {getStatusBadge(subscription.status)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Plano Atual</p>
                <p className="font-semibold">
                  {SUBSCRIPTION_PLANS[subscription.subscription_type as SubscriptionPlan]?.name || 'Gratuito'}
                </p>
              </div>
              
              {subscription.subscription_type === 'free' && (
                <div>
                  <p className="text-sm text-gray-600">Resumos Restantes</p>
                  <p className="font-semibold">{subscription.remaining_free_access || 0} de 5</p>
                </div>
              )}

              {subscription.expires_at && subscription.subscription_type !== 'free' && (
                <div>
                  <p className="text-sm text-gray-600">Próxima Cobrança</p>
                  <p className="font-semibold">
                    {new Date(subscription.expires_at).toLocaleDateString('pt-BR')}
                  </p>
                </div>
              )}

              {subscription.subscription_type !== 'free' && (
                <Button 
                  onClick={handleManageSubscription}
                  disabled={loading === 'manage'}
                  variant="outline"
                  className="w-full"
                >
                  {loading === 'manage' && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Gerenciar Assinatura
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Available Plans */}
      <div className="grid gap-6 md:grid-cols-3">
        {Object.entries(SUBSCRIPTION_PLANS).map(([planKey, plan]) => {
          const planId = planKey as SubscriptionPlan
          const isCurrentPlan = subscription?.subscription_type === planId
          const isLoading = loading === planId

          return (
            <Card 
              key={planId} 
              className={`relative ${isCurrentPlan ? 'ring-2 ring-blue-500' : ''} ${
                planId === 'PREMIUM' ? 'border-blue-200 shadow-lg' : ''
              }`}
            >
              {planId === 'PREMIUM' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white px-3 py-1">
                    <Crown className="w-3 h-3 mr-1" />
                    Mais Popular
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="text-3xl font-bold">
                  {plan.price === 0 ? (
                    'Grátis'
                  ) : (
                    <>
                      {formatPrice(plan.price)}
                      <span className="text-sm font-normal text-gray-600">
                        /{plan.interval === 'month' ? 'mês' : 'ano'}
                      </span>
                    </>
                  )}
                </div>
                {planId === 'ANNUAL' && (
                  <p className="text-sm text-green-600 font-medium">
                    Economize 17%
                  </p>
                )}
              </CardHeader>

              <CardContent className="space-y-4">
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  onClick={() => handleUpgrade(planId)}
                  disabled={isCurrentPlan || isLoading || planId === 'FREE'}
                  className={`w-full ${
                    planId === 'PREMIUM' 
                      ? 'bg-blue-600 hover:bg-blue-700' 
                      : planId === 'ANNUAL'
                      ? 'bg-green-600 hover:bg-green-700'
                      : ''
                  }`}
                  variant={planId === 'FREE' ? 'outline' : 'default'}
                >
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isCurrentPlan ? (
                    'Plano Atual'
                  ) : planId === 'FREE' ? (
                    'Plano Gratuito'
                  ) : (
                    `Assinar ${plan.name}`
                  )}
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <X className="w-4 h-4 text-red-500 mr-2" />
              <p className="text-red-700">{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle>Perguntas Frequentes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold">Posso cancelar a qualquer momento?</h4>
            <p className="text-sm text-gray-600">
              Sim, você pode cancelar sua assinatura a qualquer momento. Você continuará tendo acesso 
              aos recursos premium até o final do período de cobrança.
            </p>
          </div>
          <div>
            <h4 className="font-semibold">O que acontece quando minha assinatura gratuita acaba?</h4>
            <p className="text-sm text-gray-600">
              Você pode continuar usando a plataforma, mas terá acesso limitado a 5 resumos por mês. 
              Para acesso ilimitado, considere fazer upgrade para um plano premium.
            </p>
          </div>
          <div>
            <h4 className="font-semibold">Posso mudar de plano?</h4>
            <p className="text-sm text-gray-600">
              Sim, você pode fazer upgrade ou downgrade do seu plano a qualquer momento através 
              do portal de gerenciamento de assinatura.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
