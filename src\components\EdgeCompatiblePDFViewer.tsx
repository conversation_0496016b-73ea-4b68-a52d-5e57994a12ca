import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';

interface EdgeCompatiblePDFViewerProps {
  bookId: string;
  className?: string;
  fontSize?: number; // Font size from PDFReader (will be converted to zoom)
}

interface PDFData {
  pdf_data: string;
  pdf_filename: string;
  file_size: number;
}

const EdgeCompatiblePDFViewer: React.FC<EdgeCompatiblePDFViewerProps> = ({ bookId, className = '', fontSize = 16 }) => {
  const [pdfData, setPdfData] = useState<PDFData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);

  // Convert fontSize (12-28pt) to zoom level (50-200%)
  const zoomLevel = Math.round(((fontSize - 12) / (28 - 12)) * (200 - 50) + 50);

  useEffect(() => {
    loadPDFData();
  }, [bookId]);

  const loadPDFData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Loading PDF data for book:', bookId);

      // First, try to get the book's PDF key to use Supabase Storage
      const { data: bookData, error: bookError } = await supabase
        .from('books')
        .select('pdf_key, title')
        .eq('id', bookId)
        .single();

      if (bookError) {
        throw new Error('Livro não encontrado');
      }

      if (bookData?.pdf_key) {
        console.log('Using Supabase Storage PDF:', bookData.pdf_key);

        try {
          // Download PDF directly from Supabase Storage (try resumos bucket first)
          let pdfBlob, downloadError;
          let bucketUsed = '';

          console.log(`🔍 Attempting to download PDF: ${bookData.pdf_key}`);

          // Special handling for Freud file - try books bucket first
          let primaryBucket = 'resumos';
          let fallbackBucket = 'books';

          if (bookData.pdf_key.includes('freud_fundamentos')) {
            primaryBucket = 'books';
            fallbackBucket = 'resumos';
            console.log('📚 Freud file detected - trying books bucket first');
          }

          // Try primary bucket first
          console.log(`📁 Trying ${primaryBucket} bucket...`);
          console.log('📄 Looking for file:', bookData.pdf_key);

          const primaryResult = await supabase.storage
            .from(primaryBucket)
            .download(bookData.pdf_key);

          if (!primaryResult.error && primaryResult.data) {
            pdfBlob = primaryResult.data;
            downloadError = null;
            bucketUsed = primaryBucket;
            console.log(`✅ PDF found in ${primaryBucket} bucket: ${bookData.pdf_key}`);
          } else {
            console.log(`❌ PDF not found in ${primaryBucket} bucket: ${primaryResult.error?.message}`);

            // Try with .pdf extension if not present
            let pdfKeyWithExtension = bookData.pdf_key;
            if (!pdfKeyWithExtension.endsWith('.pdf')) {
              pdfKeyWithExtension = bookData.pdf_key + '.pdf';
              console.log('🔄 Trying with .pdf extension:', pdfKeyWithExtension);

              const primaryWithExtResult = await supabase.storage
                .from(primaryBucket)
                .download(pdfKeyWithExtension);

              if (!primaryWithExtResult.error && primaryWithExtResult.data) {
                pdfBlob = primaryWithExtResult.data;
                downloadError = null;
                bucketUsed = primaryBucket;
                console.log(`✅ PDF found with extension in ${primaryBucket} bucket: ${pdfKeyWithExtension}`);
              } else {
                console.log(`❌ PDF with extension not found in ${primaryBucket}: ${primaryWithExtResult.error?.message}`);
              }
            }

            // If still not found, try fallback bucket
            if (!pdfBlob) {
              console.log(`📁 Trying ${fallbackBucket} bucket as fallback...`);
              const fallbackResult = await supabase.storage
                .from(fallbackBucket)
                .download(bookData.pdf_key);

              if (!fallbackResult.error && fallbackResult.data) {
                pdfBlob = fallbackResult.data;
                downloadError = null;
                bucketUsed = fallbackBucket;
                console.log(`✅ PDF found in ${fallbackBucket} bucket: ${bookData.pdf_key}`);
              } else {
                pdfBlob = fallbackResult.data;
                downloadError = fallbackResult.error;
                console.log(`❌ PDF not found in ${fallbackBucket} bucket: ${fallbackResult.error?.message}`);
              }
            }
          }

          if (!downloadError && pdfBlob) {
            console.log(`✅ PDF successfully downloaded from ${bucketUsed} bucket: ${bookData.pdf_key}`);
            console.log(`📊 PDF size: ${pdfBlob.size} bytes (${Math.round(pdfBlob.size / 1024)} KB)`);

            // Create blob URL from downloaded data
            const url = URL.createObjectURL(pdfBlob);
            console.log('🔗 Blob URL created:', url);
            setPdfUrl(url);
            setLoading(false);
            return;
          } else {
            console.log(`❌ PDF download failed from both buckets: ${downloadError?.message}`);

            // Check if it's a bucket error
            if (downloadError?.message?.includes('Bucket not found')) {
              console.log('🚨 Storage bucket not found - this indicates a configuration issue');
              throw new Error('BUCKET_NOT_FOUND');
            }

            // Check if it's a file not found error
            if (downloadError?.message?.includes('Object not found')) {
              console.log(`🚨 PDF file not found in storage: ${bookData.pdf_key}`);
              throw new Error('PDF_NOT_FOUND');
            }

            console.log('Trying public URL fallback...');

            // Fallback: try public URL from resumos bucket first
            let storageData = supabase.storage
              .from('resumos')
              .getPublicUrl(bookData.pdf_key);

            if (!storageData?.data?.publicUrl) {
              // Try books bucket as fallback
              storageData = supabase.storage
                .from('books')
                .getPublicUrl(bookData.pdf_key);
            }

            if (storageData?.data?.publicUrl) {
              console.log('Using public URL:', storageData.data.publicUrl);
              setPdfUrl(storageData.data.publicUrl);
              setLoading(false);
              return;
            }
          }
        } catch (storageError) {
          console.log('Error accessing Storage:', storageError);

          // If bucket doesn't exist, skip storage methods entirely
          if (storageError.message === 'BUCKET_NOT_FOUND' ||
              storageError.message?.includes('Bucket not found')) {
            console.log('Skipping storage methods due to missing bucket');
            // Don't return here, continue to other fallbacks
          }
        }

        // Fallback: try local public/pdfs folder
        const localUrl = `/pdfs/${bookData.pdf_key}`;
        try {
          const response = await fetch(localUrl, { method: 'HEAD' });
          if (response.ok) {
            console.log('PDF found at local URL:', localUrl);
            setPdfUrl(localUrl);
            return;
          }
        } catch (fetchError) {
          console.log('PDF not found locally, trying database...');
        }
      }

      // Fallback: try to get PDF data from database
      const { data: pdfData, error: pdfError } = await supabase
        .from('pdf_files')
        .select('file_data, filename, file_size')
        .eq('book_id', bookId)
        .single();

      if (pdfError || !pdfData?.file_data) {
        console.log('No PDF data found in database for book:', bookId);
        throw new Error('CONTENT_NOT_AVAILABLE');
      }

      console.log('Using PDF data from database (fallback)');

      const pdfInfo: PDFData = {
        pdf_data: pdfData.file_data,
        pdf_filename: pdfData.filename || 'documento.pdf',
        file_size: pdfData.file_size || 0
      };

      setPdfData(pdfInfo);

      // Convert Base64 to Blob URL (fallback method)
      try {
        const binaryString = atob(pdfInfo.pdf_data);
        const bytes = new Uint8Array(binaryString.length);

        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }

        const blob = new Blob([bytes], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        setPdfUrl(url);

      } catch (conversionError) {
        console.error('PDF conversion error:', conversionError);
        throw new Error('Erro ao processar arquivo PDF');
      }

    } catch (err) {
      console.error('Error loading PDF:', err);

      // Provide user-friendly error messages
      let errorMessage = 'Erro desconhecido';
      if (err instanceof Error) {
        if (err.message === 'CONTENT_NOT_AVAILABLE') {
          errorMessage = 'Este livro não possui PDF disponível. O conteúdo pode estar disponível em formato de texto.';
        } else if (err.message === 'BUCKET_NOT_FOUND') {
          errorMessage = 'Configuração de armazenamento não encontrada. Entre em contato com o suporte.';
        } else if (err.message === 'PDF_NOT_FOUND') {
          errorMessage = 'PDF não encontrado no servidor. Este livro pode estar temporariamente indisponível.';
        } else if (err.message.includes('Bucket not found')) {
          errorMessage = 'Erro de configuração do sistema. Tente novamente mais tarde.';
        } else if (err.message.includes('Object not found')) {
          errorMessage = 'Arquivo PDF não encontrado. Este livro pode estar temporariamente indisponível.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };



  // Simple cleanup
  useEffect(() => {
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [pdfUrl]);

  if (loading) {
    return (
      <div className={`pdf-viewer-container ${className}`}>
        <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Carregando conteúdo...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`pdf-viewer-container ${className}`}>
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2 text-red-700 mb-2">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span className="font-medium">Erro ao carregar conteúdo</span>
          </div>
          <p className="text-red-600 text-sm">{error}</p>
          <button
            onClick={loadPDFData}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors text-sm"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  if (!pdfUrl) {
    console.log('PDF URL not available:', { pdfUrl, loading, error });
    return (
      <div className={`pdf-viewer-container ${className}`}>
        <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-700">Conteúdo não disponível</p>
        </div>
      </div>
    );
  }

  console.log('Rendering PDF viewer with URL:', pdfUrl);

  return (
    <div className={`pdf-viewer-container ${className}`}>
      {/* Immersive PDF viewer - zoom controlled by PDFReader font controls */}
      <div className="pdf-viewer-frame bg-white relative overflow-hidden">

        {/* CSS to hide PDF toolbar */}
        <style jsx>{`
          .pdf-viewer-frame object,
          .pdf-viewer-frame embed,
          .pdf-viewer-frame iframe {
            pointer-events: auto;
          }

          /* Hide PDF.js toolbar if it appears */
          .pdf-viewer-frame :global(.toolbar) {
            display: none !important;
          }

          .pdf-viewer-frame :global(.toolbarViewer) {
            display: none !important;
          }

          .pdf-viewer-frame :global(.secondaryToolbar) {
            display: none !important;
          }

          .pdf-viewer-frame :global(.doorHanger) {
            display: none !important;
          }

          /* Hide browser PDF controls */
          .pdf-viewer-frame :global(embed[type="application/pdf"]) {
            -webkit-appearance: none;
          }
        `}</style>

        {/* Clean PDF viewer with single vertical scrollbar */}
        <div className="pdf-container relative w-full min-h-screen bg-white">

          {/* Optimized PDF viewer with single scrollbar */}
          <object
            data={`${pdfUrl}#toolbar=0&navpanes=0&scrollbar=0&statusbar=0&messages=0&view=FitH&zoom=${zoomLevel}&page=1`}
            type="application/pdf"
            className="w-full min-h-screen overflow-hidden"
            title="Conteúdo do Livro"
            key={`object-${zoomLevel}`}
          >
            {/* Fallback 1: Embed tag with single scrollbar */}
            <embed
              src={`${pdfUrl}#toolbar=0&navpanes=0&scrollbar=0&statusbar=0&messages=0&view=FitH&zoom=${zoomLevel}&page=1`}
              type="application/pdf"
              className="w-full min-h-screen overflow-hidden"
              title="Conteúdo do Livro"
              key={`embed-${zoomLevel}`}
            />

            {/* Fallback 2: iframe with single scrollbar */}
            <iframe
              src={`${pdfUrl}#toolbar=0&navpanes=0&scrollbar=0&statusbar=0&messages=0&view=FitH&zoom=${zoomLevel}&page=1`}
              className="w-full min-h-screen overflow-hidden"
              title="Conteúdo do Livro"
              style={{ border: 'none' }}
              key={`iframe-${zoomLevel}`}
            />

            {/* Fallback 3: Direct link (minimal, only if nothing else works) */}
            <div className="flex items-center justify-center h-full bg-gray-50">
              <a
                href={pdfUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Abrir PDF
              </a>
            </div>
          </object>

          {/* Overlay to hide any remaining toolbar elements */}
          <div
            className="absolute top-0 left-0 right-0 h-12 bg-white pointer-events-none z-10"
            style={{ background: 'linear-gradient(to bottom, white 0%, transparent 100%)' }}
          />

        </div>
      </div>
    </div>
  );
};

export default EdgeCompatiblePDFViewer;
