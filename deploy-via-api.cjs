const https = require('https');
const fs = require('fs');
const path = require('path');

console.log('🚀 DEPLOY VIA API SUPABASE');
console.log('==========================\n');

// Configurações
const PROJECT_REF = 'qmeelujsnpbcdkzhcwmm';
const SUPABASE_URL = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';

// Função para fazer requisições HTTP
function makeRequest(url, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Supabase Deploy Script',
        ...headers
      }
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      const postData = typeof data === 'string' ? data : JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: responseData
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data && (method === 'POST' || method === 'PUT')) {
      const postData = typeof data === 'string' ? data : JSON.stringify(data);
      req.write(postData);
    }

    req.end();
  });
}

// Verificar se as functions estão funcionando
async function checkFunctionStatus() {
  console.log('🔍 Verificando status atual das functions...');
  
  const functions = ['stripe-checkout', 'stripe-webhook', 'stripe-portal'];
  const results = {};
  
  for (const func of functions) {
    try {
      const url = `${SUPABASE_URL}/functions/v1/${func}`;
      const response = await makeRequest(url, 'GET');
      
      if (response.statusCode === 404) {
        results[func] = 'NÃO DEPLOYADA';
        console.log(`❌ ${func}: NÃO deployada`);
      } else if (response.statusCode === 405) {
        results[func] = 'DEPLOYADA';
        console.log(`✅ ${func}: Deployada`);
      } else {
        results[func] = `STATUS ${response.statusCode}`;
        console.log(`⚠️  ${func}: Status ${response.statusCode}`);
      }
    } catch (error) {
      results[func] = 'ERRO';
      console.log(`❌ ${func}: Erro - ${error.message}`);
    }
  }
  
  return results;
}

// Testar se a stripe-portal está com a versão correta
async function testPortalVersion() {
  console.log('\n🧪 Testando versão da stripe-portal...');
  
  try {
    const response = await makeRequest(
      `${SUPABASE_URL}/functions/v1/stripe-portal`,
      'POST',
      {},
      {
        'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4`
      }
    );
    
    console.log(`📊 Status: ${response.statusCode}`);
    console.log(`📄 Response: ${response.body}`);
    
    if (response.body.includes('Customer ID é obrigatório')) {
      console.log('⚠️  VERSÃO ANTIGA (português)');
      return false;
    } else if (response.body.includes('Missing customer_id parameter')) {
      console.log('✅ VERSÃO CORRETA (inglês)');
      return true;
    } else {
      console.log('❓ Versão desconhecida');
      return false;
    }
  } catch (error) {
    console.log('❌ Erro ao testar versão:', error.message);
    return false;
  }
}

// Verificar se o código local está correto
function checkLocalCode() {
  console.log('\n📁 Verificando código local...');
  
  const portalPath = path.join(__dirname, 'supabase', 'functions', 'stripe-portal', 'index.ts');
  
  if (!fs.existsSync(portalPath)) {
    console.log('❌ Arquivo stripe-portal/index.ts não encontrado');
    return false;
  }
  
  const content = fs.readFileSync(portalPath, 'utf8');
  
  if (content.includes('Missing customer_id parameter')) {
    console.log('✅ Código local está correto (inglês)');
    return true;
  } else if (content.includes('Customer ID é obrigatório')) {
    console.log('⚠️  Código local está em português');
    return false;
  } else {
    console.log('❓ Não foi possível determinar a versão do código local');
    return false;
  }
}

// Mostrar instruções para deploy manual
function showDeployInstructions() {
  console.log('\n📋 INSTRUÇÕES PARA DEPLOY MANUAL:');
  console.log('=================================');
  console.log('');
  console.log('Como o login automático falhou, você precisa fazer o deploy manualmente:');
  console.log('');
  console.log('1. 🔐 Fazer login no Supabase CLI:');
  console.log('   npx supabase login');
  console.log('   (Siga as instruções no browser)');
  console.log('');
  console.log('2. 🔗 Linkar o projeto:');
  console.log('   npx supabase link --project-ref qmeelujsnpbcdkzhcwmm');
  console.log('');
  console.log('3. 🚀 Deploy da stripe-portal:');
  console.log('   npx supabase functions deploy stripe-portal');
  console.log('');
  console.log('4. ✅ Verificar se funcionou:');
  console.log('   node test-portal-detailed.cjs');
  console.log('');
  console.log('🎯 Alternativamente, você pode:');
  console.log('- Usar o Supabase Dashboard para fazer upload da function');
  console.log('- Ou configurar uma chave de API para deploy automático');
}

// Verificar secrets necessários
async function checkSecrets() {
  console.log('\n🔐 Verificando se secrets estão configurados...');
  
  // Não podemos verificar secrets diretamente via API sem autenticação
  // Mas podemos testar se as functions conseguem acessá-los
  
  console.log('📋 Secrets necessários:');
  console.log('- STRIPE_SECRET_KEY');
  console.log('- STRIPE_WEBHOOK_SECRET');
  console.log('- SUPABASE_URL');
  console.log('- SUPABASE_ANON_KEY');
  console.log('- SUPABASE_SERVICE_ROLE_KEY');
  console.log('');
  console.log('⚠️  Não é possível verificar secrets sem autenticação CLI');
}

// Função principal
async function main() {
  // Verificar status atual
  const functionStatus = await checkFunctionStatus();
  
  // Verificar versão da portal
  const portalCorrect = await testPortalVersion();
  
  // Verificar código local
  const localCorrect = checkLocalCode();
  
  // Verificar secrets
  await checkSecrets();
  
  console.log('\n🎯 RESUMO:');
  console.log('==========');
  console.log(`✅ stripe-checkout: ${functionStatus['stripe-checkout']}`);
  console.log(`✅ stripe-webhook: ${functionStatus['stripe-webhook']}`);
  console.log(`${portalCorrect ? '✅' : '⚠️ '} stripe-portal: ${functionStatus['stripe-portal']} ${portalCorrect ? '(versão correta)' : '(versão antiga)'}`);
  console.log(`${localCorrect ? '✅' : '❌'} Código local: ${localCorrect ? 'Correto' : 'Precisa correção'}`);
  
  if (!portalCorrect && localCorrect) {
    console.log('\n🔧 AÇÃO NECESSÁRIA:');
    console.log('===================');
    console.log('A stripe-portal function precisa ser re-deployada com a versão correta.');
    showDeployInstructions();
  } else if (portalCorrect) {
    console.log('\n🎉 TUDO FUNCIONANDO!');
    console.log('====================');
    console.log('Todas as functions estão deployadas e funcionando corretamente!');
  }
}

// Executar
main().catch(console.error);
