-- =====================================================
-- CONFIGURAÇÃO RLS PARA UPLOAD DE PDFs
-- Execute este script no SQL Editor do Supabase
-- =====================================================

-- 1. CRIAR BUCKET PARA PDFs (se não existir)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'pdfs',
  'pdfs', 
  true,
  52428800, -- 50MB limit
  ARRAY['application/pdf']
)
ON CONFLICT (id) DO NOTHING;

-- 2. CRIAR TABELA PARA ARMAZENAR PDFs COMO DADOS BINÁRIOS
CREATE TABLE IF NOT EXISTS pdf_files (
  id SERIAL PRIMARY KEY,
  book_id INTEGER REFERENCES books(id) ON DELETE CASCADE,
  filename TEXT NOT NULL,
  file_data BYTEA NOT NULL,
  file_size INTEGER NOT NULL,
  mime_type TEXT DEFAULT 'application/pdf',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. ADICIONAR COLUNAS À TABELA BOOKS PARA REFERENCIAR PDFs
ALTER TABLE books 
ADD COLUMN IF NOT EXISTS has_pdf BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS pdf_file_id INTEGER REFERENCES pdf_files(id);

-- 4. POLÍTICAS RLS PARA STORAGE (BUCKET PDFs)
-- Permitir leitura pública dos PDFs
CREATE POLICY "Public PDF Access" ON storage.objects
FOR SELECT USING (bucket_id = 'pdfs');

-- Permitir upload de PDFs (temporário para upload inicial)
CREATE POLICY "Allow PDF Upload" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'pdfs');

-- Permitir atualização de PDFs
CREATE POLICY "Allow PDF Update" ON storage.objects
FOR UPDATE USING (bucket_id = 'pdfs');

-- 5. POLÍTICAS RLS PARA TABELA PDF_FILES
-- Habilitar RLS na tabela
ALTER TABLE pdf_files ENABLE ROW LEVEL SECURITY;

-- Permitir leitura pública dos PDFs
CREATE POLICY "Public PDF Files Read" ON pdf_files
FOR SELECT USING (true);

-- Permitir inserção de PDFs (temporário para upload inicial)
CREATE POLICY "Allow PDF Files Insert" ON pdf_files
FOR INSERT WITH CHECK (true);

-- 6. FUNÇÃO PARA SERVIR PDFs COMO RESPOSTA HTTP
CREATE OR REPLACE FUNCTION serve_pdf(pdf_id INTEGER)
RETURNS BYTEA
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  pdf_data BYTEA;
BEGIN
  SELECT file_data INTO pdf_data
  FROM pdf_files
  WHERE id = pdf_id;
  
  RETURN pdf_data;
END;
$$;

-- 7. FUNÇÃO PARA OBTER PDF POR BOOK_ID
CREATE OR REPLACE FUNCTION get_pdf_by_book_id(book_id_param INTEGER)
RETURNS TABLE(
  id INTEGER,
  filename TEXT,
  file_size INTEGER,
  mime_type TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pf.id,
    pf.filename,
    pf.file_size,
    pf.mime_type
  FROM pdf_files pf
  WHERE pf.book_id = book_id_param;
END;
$$;

-- 8. TRIGGER PARA ATUALIZAR TIMESTAMP
CREATE OR REPLACE FUNCTION update_pdf_files_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_pdf_files_updated_at
  BEFORE UPDATE ON pdf_files
  FOR EACH ROW
  EXECUTE FUNCTION update_pdf_files_updated_at();

-- 9. ÍNDICES PARA PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_pdf_files_book_id ON pdf_files(book_id);
CREATE INDEX IF NOT EXISTS idx_books_pdf_file_id ON books(pdf_file_id);

-- 10. COMENTÁRIOS PARA DOCUMENTAÇÃO
COMMENT ON TABLE pdf_files IS 'Armazena arquivos PDF como dados binários para acesso global';
COMMENT ON COLUMN pdf_files.book_id IS 'Referência ao livro na tabela books';
COMMENT ON COLUMN pdf_files.file_data IS 'Dados binários do arquivo PDF';
COMMENT ON COLUMN pdf_files.file_size IS 'Tamanho do arquivo em bytes';

-- =====================================================
-- INSTRUÇÕES DE USO:
-- =====================================================
-- 1. Execute este script completo no SQL Editor do Supabase
-- 2. Verifique se todas as tabelas e políticas foram criadas
-- 3. Execute o script de upload de PDFs novamente
-- 4. Após o upload, você pode remover as políticas de INSERT se desejar:
--    DROP POLICY "Allow PDF Upload" ON storage.objects;
--    DROP POLICY "Allow PDF Files Insert" ON pdf_files;
-- =====================================================
