const https = require('https');

console.log('🧪 TESTE RÁPIDO - STRIPE PORTAL');
console.log('===============================\n');

const PORTAL_URL = 'https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';

function makeRequest(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname,
      method: method,
      headers: {
        'Authorization': `Bearer ${ANON_KEY}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Node.js Test Client'
      }
    };

    if (data && method === 'POST') {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: responseData
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data && method === 'POST') {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testPortal() {
  try {
    const response = await makeRequest(PORTAL_URL, 'POST', {});
    
    console.log(`📊 Status: ${response.statusCode}`);
    console.log(`📄 Response: ${response.body}`);
    
    if (response.body.includes('Customer ID é obrigatório')) {
      console.log('\n❌ AINDA EM PORTUGUÊS');
      console.log('   Precisa fazer deploy da versão correta');
      console.log('   Siga as instruções em: DEPLOY-PORTAL-CORRETO.md');
    } else if (response.body.includes('Missing customer_id parameter')) {
      console.log('\n✅ VERSÃO CORRETA (INGLÊS)');
      console.log('   Function está funcionando perfeitamente!');
    } else {
      console.log('\n❓ RESPOSTA INESPERADA');
      console.log('   Verifique se a function foi deployada corretamente');
    }
    
  } catch (error) {
    console.log('❌ Erro:', error.message);
  }
}

testPortal();
