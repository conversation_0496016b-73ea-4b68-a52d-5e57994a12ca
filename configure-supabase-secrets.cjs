const { execSync } = require('child_process');

console.log('🔐 CONFIGURANDO VARIÁVEIS DE AMBIENTE NO SUPABASE');
console.log('================================================\n');

// Configurações
const secrets = {
  'STRIPE_SECRET_KEY': 'sk_test_51RIcDWQXmlk54RXco22WsOUYY2jtE27RhFyvbaaiInFpIDD5yFcbQNPjSilbv6pzKxq1YuihBgAy4ria1OV3l2mY00gw9ED5fy',
  'STRIPE_WEBHOOK_SECRET': 'whsec_rSOSlhBwwbkRSVfgqla96EctF9NTcL9c',
  'SUPABASE_URL': 'https://qmeelujsnpbcdkzhcwmm.supabase.co',
  'SUPABASE_ANON_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4',
  'SUPABASE_SERVICE_ROLE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjM2MjM0NSwiZXhwIjoyMDYxOTM4MzQ1fQ.example_service_role_key_here'
};

function runCommand(command, description) {
  console.log(`🔄 ${description}...`);
  try {
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    console.log(`✅ ${description} - Sucesso`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} - Erro:`, error.message);
    return false;
  }
}

function checkSupabaseCLI() {
  console.log('🔍 Verificando Supabase CLI...');
  try {
    const version = execSync('supabase --version', { encoding: 'utf8' });
    console.log(`✅ Supabase CLI encontrado: ${version.trim()}`);
    return true;
  } catch (error) {
    console.log('❌ Supabase CLI não encontrado!');
    console.log('\n📥 Para instalar:');
    console.log('   npm install -g supabase');
    console.log('   ou visite: https://supabase.com/docs/guides/cli\n');
    return false;
  }
}

function checkProjectLink() {
  console.log('🔗 Verificando link do projeto...');
  try {
    const status = execSync('supabase status', { encoding: 'utf8' });
    if (status.includes('qmeelujsnpbcdkzhcwmm') || status.includes('Local')) {
      console.log('✅ Projeto linkado');
      return true;
    }
  } catch (error) {
    // Projeto não linkado
  }
  
  console.log('🔄 Linkando projeto...');
  return runCommand('supabase link --project-ref qmeelujsnpbcdkzhcwmm', 'Link do projeto');
}

function setSecrets() {
  console.log('\n🔐 Configurando secrets...');
  
  let successCount = 0;
  let totalSecrets = Object.keys(secrets).length;
  
  for (const [key, value] of Object.entries(secrets)) {
    console.log(`\n📝 Configurando ${key}...`);
    
    // Escapar aspas no valor
    const escapedValue = value.replace(/"/g, '\\"');
    const command = `supabase secrets set ${key}="${escapedValue}"`;
    
    if (runCommand(command, `Set ${key}`)) {
      successCount++;
    }
  }
  
  console.log(`\n📊 Resultado: ${successCount}/${totalSecrets} secrets configurados`);
  return successCount === totalSecrets;
}

function listSecrets() {
  console.log('\n📋 Verificando secrets configurados...');
  try {
    const output = execSync('supabase secrets list', { encoding: 'utf8' });
    console.log(output);
    return true;
  } catch (error) {
    console.error('❌ Erro ao listar secrets:', error.message);
    return false;
  }
}

function showInstructions() {
  console.log('\n🎯 PRÓXIMOS PASSOS:');
  console.log('==================');
  console.log('1. ✅ Secrets configurados no Supabase');
  console.log('2. 🚀 Deploy das Edge Functions:');
  console.log('   supabase functions deploy stripe-checkout');
  console.log('   supabase functions deploy stripe-webhook');
  console.log('   supabase functions deploy stripe-portal');
  console.log('');
  console.log('3. 📊 Verificar logs:');
  console.log('   supabase functions logs stripe-webhook');
  console.log('');
  console.log('4. 🧪 Testar integração na aplicação');
  console.log('');
  console.log('🔗 URLs das Functions:');
  console.log('   Checkout: https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-checkout');
  console.log('   Webhook:  https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-webhook');
  console.log('   Portal:   https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal');
}

// Executar configuração
async function main() {
  // Verificar CLI
  if (!checkSupabaseCLI()) {
    process.exit(1);
  }
  
  // Verificar link do projeto
  if (!checkProjectLink()) {
    console.log('❌ Falha ao linkar projeto');
    process.exit(1);
  }
  
  // Configurar secrets
  if (!setSecrets()) {
    console.log('⚠️  Alguns secrets falharam. Verifique os erros acima.');
  }
  
  // Listar secrets para verificação
  listSecrets();
  
  // Mostrar próximos passos
  showInstructions();
  
  console.log('\n🎉 Configuração de secrets concluída!');
}

main().catch(error => {
  console.error('❌ Erro na configuração:', error.message);
  process.exit(1);
});
