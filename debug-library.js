// Script para debugar a biblioteca - Execute no console do navegador
console.log('🔍 ANALISANDO BIBLIOTECA...');

async function analyzeLibrary() {
  try {
    // 1. Verificar o que está no Supabase
    console.log('\n📊 CONSULTANDO SUPABASE...');
    const { data: supabaseBooks, error } = await supabase
      .from('books')
      .select('id, title, author, pdf_key, category, is_featured')
      .order('id');
      
    if (error) {
      console.error('❌ Erro no Supabase:', error);
      return;
    }
    
    console.log(`✅ Supabase retornou ${supabaseBooks.length} livros`);
    
    // 2. Aplicar os mesmos filtros que o Dashboard usa
    console.log('\n🧹 APLICANDO FILTROS...');
    
    const isProblematicBook = (book) => {
      if (!book.id || !book.title || typeof book.title !== 'string') {
        return true;
      }
      
      const title = book.title.toLowerCase().trim();
      
      return (
        title.length === 0 ||
        book.id <= 0 ||
        title.includes(' 3') || title.includes(' 5') || title.includes(' 6') ||
        title.length <= 3 ||
        title.startsWith('[removed]') ||
        book.category === 'REMOVED' ||
        title.length > 100 ||
        (title.includes('autores') && title.includes('autores'))
      );
    };
    
    const cleanBooks = supabaseBooks.filter(book => !isProblematicBook(book));
    console.log(`✅ Após filtros: ${cleanBooks.length} livros limpos`);
    
    if (cleanBooks.length !== supabaseBooks.length) {
      const problematic = supabaseBooks.filter(book => isProblematicBook(book));
      console.log('❌ Livros filtrados como problemáticos:');
      problematic.forEach(book => {
        console.log(`   ID ${book.id}: "${book.title}"`);
      });
    }
    
    // 3. Aplicar remoção de duplicatas (simplificada)
    const uniqueBooks = [];
    const seenTitles = new Set();
    
    cleanBooks.forEach(book => {
      const normalizedTitle = book.title.toLowerCase().trim();
      if (!seenTitles.has(normalizedTitle)) {
        seenTitles.add(normalizedTitle);
        uniqueBooks.push(book);
      } else {
        console.log(`🔄 Removendo duplicata: ID ${book.id} - "${book.title}"`);
      }
    });
    
    console.log(`✅ Após remoção de duplicatas: ${uniqueBooks.length} livros únicos`);
    
    // 4. Mostrar resultado final
    console.log('\n📚 LIVROS FINAIS NA BIBLIOTECA:');
    uniqueBooks.forEach((book, index) => {
      const type = book.pdf_key ? '✅ PDF' : '📄 Text';
      console.log(`${index + 1}. ${type} ID ${book.id}: "${book.title}" - ${book.author}`);
    });
    
    // 5. Estatísticas
    const withPDF = uniqueBooks.filter(book => book.pdf_key && book.pdf_key.trim() !== '');
    const textOnly = uniqueBooks.filter(book => !book.pdf_key || book.pdf_key.trim() === '');
    
    console.log('\n📊 ESTATÍSTICAS:');
    console.log(`   Total de livros: ${uniqueBooks.length}`);
    console.log(`   Com PDF: ${withPDF.length}`);
    console.log(`   Apenas texto: ${textOnly.length}`);
    console.log(`   Em destaque: ${uniqueBooks.filter(book => book.is_featured).length}`);
    
    // 6. Verificar PDFs específicos da lista
    console.log('\n🔍 VERIFICANDO PDFs DA LISTA FORNECIDA:');
    const expectedPDFs = [
      'gestalt_terapia_fundamentos_epistemologicos_e_influencias_filosoficas_baseado_na_colecao_gestalt_terapia_3.pdf',
      'habitos_atomicos_james_clear_6.pdf',
      'handbook_of_psychology_personality_and_social_psychology_editores_theodore_millon_melvin_j_lerner_irving_b_weiner_autor_desconhecido_3.pdf',
      'john_bowlby_e_a_teoria_do_apego_jeremy_holmes_3.pdf',
      'martin_heidegger_e_a_psicologia_existencial-humanista_fundamentos_e_aplicacoes_martin_heidegger_conteudo-fonte.pdf',
      'mulheres_que_amam_demais_robin_norwood_3.pdf',
      'o_cerebro_no_esporte_superando_bloqueios_e_melhorando_a_performance_autores_david_grand_alan_goldberg_david_grand_alan_goldberg_3.pdf',
      'o_eu_e_o_inconsciente_c_g_jung_3.pdf',
      'o_homem_e_seus_simbolos_carl_g_jung_3.pdf',
      'o_poder_do_habito_charles_duhigg_3.pdf',
      'pai_rico_pai_pobre_robert_t_kiyosaki_3.pdf',
      'psicodiagnosticoo completa_baseado_na_obra_de_jurema_alcides_cunha.pdf',
      'psicodiagnostico_uma_visao_contemporanea_claudio_simon_hutz_et_al..pdf',
      'psicopatologia_e_semiologia_dos_transtornos_mentais_expansao_dalgalarrondo_baseado_em_dalgalarrondo_3.pdf',
      'teorias_da_personalidade_autores_james_fadiman_robert_frager_james_fadiman_robert_frager_3.pdf',
      'terapia_cognitivo_comportamental_para_iniciantes_autor_desconhecido_3.pdf',
      'tornar_se_pessoa_carl_rogers_3.pdf'
    ];
    
    const currentPDFs = withPDF.map(book => book.pdf_key);
    
    expectedPDFs.forEach(expectedPDF => {
      const found = currentPDFs.includes(expectedPDF);
      console.log(`${found ? '✅' : '❌'} ${expectedPDF}`);
    });
    
    console.log('\n🎯 ANÁLISE COMPLETA!');
    return uniqueBooks;
    
  } catch (error) {
    console.error('❌ Erro na análise:', error);
  }
}

// Executar análise
analyzeLibrary();
