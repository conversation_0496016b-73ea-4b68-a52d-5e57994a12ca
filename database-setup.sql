-- Execute this SQL in your Supabase SQL Editor to set up the database schema
-- This creates all necessary tables and RLS policies for the book summaries platform

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create user_reading_progress table
CREATE TABLE IF NOT EXISTS user_reading_progress (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  summary_id text NOT NULL,
  progress_percentage numeric(5,2) DEFAULT 0 NOT NULL CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  is_completed boolean DEFAULT false NOT NULL,
  is_favorited boolean DEFAULT false NOT NULL,
  last_read_at timestamptz DEFAULT now() NOT NULL,
  reading_time_minutes integer DEFAULT 0 NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  UNIQUE(user_id, summary_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_reading_progress_user_id ON user_reading_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_reading_progress_summary_id ON user_reading_progress(summary_id);
CREATE INDEX IF NOT EXISTS idx_user_reading_progress_is_favorited ON user_reading_progress(user_id, is_favorited) WHERE is_favorited = true;
CREATE INDEX IF NOT EXISTS idx_user_reading_progress_is_completed ON user_reading_progress(user_id, is_completed) WHERE is_completed = true;
CREATE INDEX IF NOT EXISTS idx_user_reading_progress_in_progress ON user_reading_progress(user_id, progress_percentage) WHERE progress_percentage > 0 AND progress_percentage < 100;
CREATE INDEX IF NOT EXISTS idx_user_reading_progress_last_read ON user_reading_progress(user_id, last_read_at DESC);

-- Create user_subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_type text NOT NULL DEFAULT 'free' CHECK (subscription_type IN ('free', 'premium')),
  status text NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'expired')),
  started_at timestamptz DEFAULT now() NOT NULL,
  expires_at timestamptz,
  remaining_free_access integer DEFAULT 5 NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  UNIQUE(user_id)
);

-- Create summaries table (for API compatibility)
CREATE TABLE IF NOT EXISTS summaries (
  id text PRIMARY KEY,
  book_id integer REFERENCES books(id) ON DELETE CASCADE,
  title text NOT NULL,
  content text,
  key_points jsonb DEFAULT '[]',
  word_count integer DEFAULT 0,
  estimated_reading_time integer DEFAULT 20,
  is_published boolean DEFAULT true,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL
);

-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  slug text NOT NULL UNIQUE,
  description text,
  created_at timestamptz DEFAULT now() NOT NULL
);

-- Insert default categories
INSERT INTO categories (name, slug, description) VALUES
  ('Produtividade', 'produtividade', 'Livros sobre produtividade e eficiência'),
  ('Negócios', 'negocios', 'Livros sobre negócios e empreendedorismo'),
  ('Desenvolvimento Pessoal', 'desenvolvimento-pessoal', 'Livros sobre crescimento pessoal'),
  ('Psicologia', 'psicologia', 'Livros sobre psicologia e comportamento'),
  ('Finanças', 'financas', 'Livros sobre finanças e investimentos'),
  ('Liderança', 'lideranca', 'Livros sobre liderança e gestão')
ON CONFLICT (slug) DO NOTHING;

-- Add category_id to books table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'books' AND column_name = 'category_id') THEN
    ALTER TABLE books ADD COLUMN category_id uuid REFERENCES categories(id);
  END IF;
END $$;

-- Populate summaries table from books table
INSERT INTO summaries (id, book_id, title, estimated_reading_time)
SELECT 
  id::text,
  id,
  title,
  COALESCE(duration, 20)
FROM books
ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  estimated_reading_time = EXCLUDED.estimated_reading_time;

-- Enable RLS on all tables
ALTER TABLE user_reading_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own reading progress" ON user_reading_progress;
DROP POLICY IF EXISTS "Users can insert their own reading progress" ON user_reading_progress;
DROP POLICY IF EXISTS "Users can update their own reading progress" ON user_reading_progress;
DROP POLICY IF EXISTS "Users can delete their own reading progress" ON user_reading_progress;

DROP POLICY IF EXISTS "Users can view their own subscription" ON user_subscriptions;
DROP POLICY IF EXISTS "Users can insert their own subscription" ON user_subscriptions;
DROP POLICY IF EXISTS "Users can update their own subscription" ON user_subscriptions;

DROP POLICY IF EXISTS "Anyone can view published summaries" ON summaries;
DROP POLICY IF EXISTS "Anyone can view categories" ON categories;

-- RLS Policies for user_reading_progress
CREATE POLICY "Users can view their own reading progress" ON user_reading_progress
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own reading progress" ON user_reading_progress
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reading progress" ON user_reading_progress
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reading progress" ON user_reading_progress
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for user_subscriptions
CREATE POLICY "Users can view their own subscription" ON user_subscriptions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own subscription" ON user_subscriptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own subscription" ON user_subscriptions
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for summaries (public read access)
CREATE POLICY "Anyone can view published summaries" ON summaries
  FOR SELECT USING (is_published = true);

-- RLS Policies for categories (public read access)
CREATE POLICY "Anyone can view categories" ON categories
  FOR SELECT USING (true);

-- Create function to automatically create user subscription on signup
CREATE OR REPLACE FUNCTION create_user_subscription()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO user_subscriptions (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic subscription creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION create_user_subscription();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing triggers
DROP TRIGGER IF EXISTS update_user_reading_progress_updated_at ON user_reading_progress;
DROP TRIGGER IF EXISTS update_user_subscriptions_updated_at ON user_subscriptions;
DROP TRIGGER IF EXISTS update_summaries_updated_at ON summaries;

-- Create triggers for updated_at
CREATE TRIGGER update_user_reading_progress_updated_at
  BEFORE UPDATE ON user_reading_progress
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at
  BEFORE UPDATE ON user_subscriptions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_summaries_updated_at
  BEFORE UPDATE ON summaries
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a view for easier dashboard queries
CREATE OR REPLACE VIEW user_dashboard_data AS
SELECT 
  urp.*,
  s.title as summary_title,
  s.estimated_reading_time,
  b.title as book_title,
  b.author as book_author,
  b.cover_image_url
FROM user_reading_progress urp
JOIN summaries s ON urp.summary_id = s.id
JOIN books b ON s.book_id = b.id
WHERE s.is_published = true;

-- Grant necessary permissions
GRANT SELECT ON user_dashboard_data TO authenticated;
GRANT ALL ON user_reading_progress TO authenticated;
GRANT ALL ON user_subscriptions TO authenticated;
GRANT SELECT ON summaries TO authenticated;
GRANT SELECT ON categories TO authenticated;
GRANT SELECT ON books TO authenticated;
