-- Limpeza Completa da Biblioteca - <PERSON><PERSON><PERSON> Corrigida (sem tabela summaries)
-- Execute este SQL no Supabase Dashboard > SQL Editor

-- STEP 1: Verificar quais tabelas existem
SELECT 'VERIFICANDO TABELAS EXISTENTES' as step;

SELECT 
    table_name,
    'EXISTS' as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('books', 'book_contents', 'pdf_files', 'user_reading_progress', 'summaries')
ORDER BY table_name;

-- STEP 2: Mostrar livros que serão removidos/corrigidos
SELECT 'LIVROS QUE SERÃO PROCESSADOS' as step;

-- Livros com conteúdo placeholder que serão removidos
SELECT 
    'REMOVER - Conteúdo Placeholder' as action,
    b.id,
    b.title,
    b.author,
    b.category
FROM books b
LEFT JOIN book_contents bc ON bc.book_id = b.id
WHERE (bc.content::text LIKE '%conteúdo completo está sendo processado%'
       OR bc.content::text LIKE '%Nossa equipe está trabalhando%'
       OR (bc.content IS NULL AND (b.pdf_key IS NULL OR b.pdf_key = '')))
AND (
    LOWER(b.title) LIKE '%fundamentos da psicologia analítica%' 
    OR (LOWER(b.author) LIKE '%carl gustav jung%' AND LOWER(b.title) LIKE '%fundamentos%')
    OR (LOWER(b.title) LIKE '%freud%' AND LOWER(b.title) LIKE '%fundamentos%')
)

UNION ALL

-- Livro para traduzir título
SELECT 
    'TRADUZIR TÍTULO' as action,
    id,
    title,
    author,
    category
FROM books 
WHERE LOWER(title) LIKE '%determined%science%life%free%will%'
   OR LOWER(author) LIKE '%robert sapolsky%';

-- STEP 3: Remover dados de progresso dos livros problemáticos
DELETE FROM user_reading_progress 
WHERE book_id IN (
    SELECT b.id::text
    FROM books b
    LEFT JOIN book_contents bc ON bc.book_id = b.id
    WHERE (bc.content::text LIKE '%conteúdo completo está sendo processado%'
           OR bc.content::text LIKE '%Nossa equipe está trabalhando%'
           OR (bc.content IS NULL AND (b.pdf_key IS NULL OR b.pdf_key = '')))
    AND (
        LOWER(b.title) LIKE '%fundamentos da psicologia analítica%' 
        OR (LOWER(b.author) LIKE '%carl gustav jung%' AND LOWER(b.title) LIKE '%fundamentos%')
        OR (LOWER(b.title) LIKE '%freud%' AND LOWER(b.title) LIKE '%fundamentos%')
    )
);

-- STEP 4: Remover conteúdo placeholder
DELETE FROM book_contents 
WHERE book_id IN (
    SELECT b.id
    FROM books b
    LEFT JOIN book_contents bc ON bc.book_id = b.id
    WHERE (bc.content::text LIKE '%conteúdo completo está sendo processado%'
           OR bc.content::text LIKE '%Nossa equipe está trabalhando%'
           OR (bc.content IS NULL AND (b.pdf_key IS NULL OR b.pdf_key = '')))
    AND (
        LOWER(b.title) LIKE '%fundamentos da psicologia analítica%' 
        OR (LOWER(b.author) LIKE '%carl gustav jung%' AND LOWER(b.title) LIKE '%fundamentos%')
        OR (LOWER(b.title) LIKE '%freud%' AND LOWER(b.title) LIKE '%fundamentos%')
    )
);

-- STEP 5: Remover PDFs relacionados (se a tabela existir)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'pdf_files' AND table_schema = 'public') THEN
        DELETE FROM pdf_files 
        WHERE book_id IN (
            SELECT b.id::text
            FROM books b
            LEFT JOIN book_contents bc ON bc.book_id = b.id
            WHERE (bc.content::text LIKE '%conteúdo completo está sendo processado%'
                   OR bc.content::text LIKE '%Nossa equipe está trabalhando%'
                   OR (bc.content IS NULL AND (b.pdf_key IS NULL OR b.pdf_key = '')))
            AND (
                LOWER(b.title) LIKE '%fundamentos da psicologia analítica%' 
                OR (LOWER(b.author) LIKE '%carl gustav jung%' AND LOWER(b.title) LIKE '%fundamentos%')
                OR (LOWER(b.title) LIKE '%freud%' AND LOWER(b.title) LIKE '%fundamentos%')
            )
        );
        RAISE NOTICE 'PDFs relacionados removidos da tabela pdf_files';
    ELSE
        RAISE NOTICE 'Tabela pdf_files não existe - pulando';
    END IF;
END $$;

-- STEP 6: Remover os livros problemáticos
DELETE FROM books 
WHERE id IN (
    SELECT b.id
    FROM books b
    LEFT JOIN book_contents bc ON bc.book_id = b.id
    WHERE (bc.content::text LIKE '%conteúdo completo está sendo processado%'
           OR bc.content::text LIKE '%Nossa equipe está trabalhando%'
           OR (bc.content IS NULL AND (b.pdf_key IS NULL OR b.pdf_key = '')))
    AND (
        LOWER(b.title) LIKE '%fundamentos da psicologia analítica%' 
        OR (LOWER(b.author) LIKE '%carl gustav jung%' AND LOWER(b.title) LIKE '%fundamentos%')
        OR (LOWER(b.title) LIKE '%freud%' AND LOWER(b.title) LIKE '%fundamentos%')
    )
);

-- STEP 7: Traduzir título do livro "Determined"
UPDATE books 
SET 
    title = 'Determinado: Uma Ciência da Vida Sem Livre Arbítrio',
    description = COALESCE(description, 'Investigação científica sobre livre arbítrio e determinismo comportamental'),
    category = 'Neurociência',
    updated_at = now()
WHERE LOWER(title) LIKE '%determined%science%life%free%will%'
   OR (LOWER(author) LIKE '%robert sapolsky%' AND LOWER(title) LIKE '%determined%');

-- STEP 8: Remover outros livros com conteúdo placeholder
DELETE FROM user_reading_progress 
WHERE book_id IN (
    SELECT b.id::text
    FROM books b
    JOIN book_contents bc ON bc.book_id = b.id
    WHERE bc.content::text LIKE '%conteúdo completo está sendo processado%'
       OR bc.content::text LIKE '%Nossa equipe está trabalhando%'
       OR bc.content::text LIKE '%Aguarde o processamento completo%'
);

DELETE FROM book_contents 
WHERE content::text LIKE '%conteúdo completo está sendo processado%'
   OR content::text LIKE '%Nossa equipe está trabalhando%'
   OR content::text LIKE '%Aguarde o processamento completo%';

-- STEP 9: Remover livros sem conteúdo nem PDF
DELETE FROM user_reading_progress 
WHERE book_id IN (
    SELECT b.id::text
    FROM books b
    LEFT JOIN book_contents bc ON bc.book_id = b.id
    WHERE bc.content IS NULL 
      AND (b.pdf_key IS NULL OR b.pdf_key = '')
      AND b.title NOT IN (
          'Hábitos Atômicos',
          'Pai Rico, Pai Pobre',
          'O Investidor Inteligente',
          'Cem Anos de Solidão'
      )
);

DELETE FROM books 
WHERE id IN (
    SELECT b.id
    FROM books b
    LEFT JOIN book_contents bc ON bc.book_id = b.id
    WHERE bc.content IS NULL 
      AND (b.pdf_key IS NULL OR b.pdf_key = '')
      AND b.title NOT IN (
          'Hábitos Atômicos',
          'Pai Rico, Pai Pobre',
          'O Investidor Inteligente',
          'Cem Anos de Solidão'
      )
);

-- STEP 10: Limpar duplicatas mantendo a versão mais recente
WITH duplicates AS (
    SELECT 
        id,
        title,
        author,
        ROW_NUMBER() OVER (
            PARTITION BY LOWER(TRIM(title)), LOWER(TRIM(author)) 
            ORDER BY 
                CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 ELSE 2 END,
                CASE WHEN is_featured THEN 1 ELSE 2 END,
                created_at DESC
        ) as rn
    FROM books
)
DELETE FROM user_reading_progress 
WHERE book_id IN (
    SELECT id::text FROM duplicates WHERE rn > 1
);

DELETE FROM book_contents 
WHERE book_id IN (
    SELECT id FROM duplicates WHERE rn > 1
);

DELETE FROM books 
WHERE id IN (
    SELECT id FROM duplicates WHERE rn > 1
);

-- STEP 11: Otimizar livros em destaque
UPDATE books 
SET is_featured = false 
WHERE is_featured = true 
  AND id NOT IN (
      SELECT id FROM (
          SELECT 
              id,
              ROW_NUMBER() OVER (
                  ORDER BY 
                      CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 ELSE 2 END,
                      CASE WHEN category IN ('Desenvolvimento Pessoal', 'Finanças', 'Produtividade', 'Psicologia', 'Literatura', 'Neurociência') THEN 1 ELSE 2 END,
                      created_at DESC
              ) as rn
          FROM books 
          WHERE is_featured = true
      ) ranked 
      WHERE rn <= 15  -- Manter apenas 15 livros em destaque
  );

-- STEP 12: Verificação final
SELECT 'RESULTADO DA LIMPEZA' as step;

SELECT 
    'Total de livros após limpeza' as metric,
    COUNT(*) as count
FROM books

UNION ALL

SELECT 
    'Livros em destaque' as metric,
    COUNT(*) as count
FROM books 
WHERE is_featured = true

UNION ALL

SELECT 
    'Livros com PDF' as metric,
    COUNT(*) as count
FROM books 
WHERE pdf_key IS NOT NULL AND pdf_key != ''

UNION ALL

SELECT 
    'Livros com conteúdo estruturado' as metric,
    COUNT(*) as count
FROM books b
JOIN book_contents bc ON bc.book_id = b.id
WHERE bc.content IS NOT NULL

UNION ALL

SELECT 
    'Livros problemáticos restantes' as metric,
    COUNT(*) as count
FROM books b
LEFT JOIN book_contents bc ON bc.book_id = b.id
WHERE bc.content IS NULL 
  AND (b.pdf_key IS NULL OR b.pdf_key = '');

-- STEP 13: Mostrar livros em destaque após limpeza
SELECT 'LIVROS EM DESTAQUE APÓS LIMPEZA' as step;

SELECT 
    id,
    title,
    author,
    category,
    duration,
    CASE 
        WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN '✅ TEM PDF'
        ELSE '📄 CONTEÚDO ESTRUTURADO'
    END as content_type
FROM books 
WHERE is_featured = true
ORDER BY 
    CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 ELSE 2 END,
    category,
    title;

-- Mensagem final
SELECT 
    '✅ LIMPEZA COMPLETA REALIZADA' as status,
    'Biblioteca otimizada e livros problemáticos removidos' as message,
    'Título "Determined" traduzido para português' as translation_status;
