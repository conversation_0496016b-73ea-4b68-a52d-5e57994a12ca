// Script para diagnosticar problemas de autenticação
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugAuth() {
  console.log('🔍 DIAGNÓSTICO DE AUTENTICAÇÃO');
  console.log('=' .repeat(50));

  // 1. Testar conexão com Supabase
  console.log('\n1. 📡 Testando conexão com Supabase...');
  try {
    const { data, error } = await supabase.from('books').select('count').limit(1);
    if (error) {
      console.log('❌ Erro na conexão:', error.message);
    } else {
      console.log('✅ Conexão com Supabase OK');
    }
  } catch (err) {
    console.log('❌ Erro de conexão:', err.message);
  }

  // 2. Testar registro de usuário
  console.log('\n2. 👤 Testando registro de usuário...');
  const testEmail = `test-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';
  
  try {
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        emailRedirectTo: `${window?.location?.origin || 'http://localhost:5175'}/auth/callback`
      }
    });

    if (error) {
      console.log('❌ Erro no registro:', error.message);
      console.log('   Código:', error.status);
      console.log('   Detalhes:', JSON.stringify(error, null, 2));
    } else {
      console.log('✅ Registro bem-sucedido!');
      console.log('   User ID:', data.user?.id);
      console.log('   Email:', data.user?.email);
      console.log('   Confirmação necessária:', !data.user?.email_confirmed_at);
      
      // Limpar o usuário de teste
      if (data.user) {
        await supabase.auth.signOut();
      }
    }
  } catch (err) {
    console.log('❌ Erro inesperado no registro:', err.message);
  }

  // 3. Verificar configurações de auth
  console.log('\n3. ⚙️ Verificando configurações...');
  try {
    // Tentar obter configurações do projeto
    const { data: settings, error: settingsError } = await supabase.auth.getSession();
    console.log('✅ Sessão atual:', settings.session ? 'Ativa' : 'Nenhuma');
  } catch (err) {
    console.log('❌ Erro ao verificar sessão:', err.message);
  }

  // 4. Testar login com usuário existente (se houver)
  console.log('\n4. 🔐 Testando login...');
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Admin123!'
    });

    if (error) {
      console.log('❌ Erro no login (esperado se não existir):', error.message);
    } else {
      console.log('✅ Login bem-sucedido com usuário admin');
      await supabase.auth.signOut();
    }
  } catch (err) {
    console.log('❌ Erro inesperado no login:', err.message);
  }

  // 5. Verificar triggers e funções
  console.log('\n5. 🔧 Verificando triggers do banco...');
  try {
    const { data, error } = await supabase
      .from('pg_trigger')
      .select('tgname, tgenabled')
      .ilike('tgname', '%auth%');
    
    if (error) {
      console.log('❌ Não foi possível verificar triggers:', error.message);
    } else {
      console.log('✅ Triggers encontrados:', data?.length || 0);
      data?.forEach(trigger => {
        console.log(`   - ${trigger.tgname}: ${trigger.tgenabled ? 'Ativo' : 'Inativo'}`);
      });
    }
  } catch (err) {
    console.log('❌ Erro ao verificar triggers:', err.message);
  }

  console.log('\n' + '=' .repeat(50));
  console.log('🏁 DIAGNÓSTICO CONCLUÍDO');
}

// Executar diagnóstico
debugAuth().catch(console.error);
