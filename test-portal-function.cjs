const https = require('https');

console.log('🧪 TESTANDO STRIPE-PORTAL FUNCTION');
console.log('==================================\n');

const PORTAL_URL = 'https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';

function makeRequest(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname,
      method: method,
      headers: {
        'Authorization': `Bearer ${ANON_KEY}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Node.js Test Client'
      }
    };

    if (data && method === 'POST') {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: responseData
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data && method === 'POST') {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testPortalFunction() {
  console.log('🔍 Testando se a function existe...');
  
  // Teste 1: GET request (deve retornar 405 Method Not Allowed se existir)
  try {
    const getResponse = await makeRequest(PORTAL_URL, 'GET');
    console.log(`📊 GET Request - Status: ${getResponse.statusCode}`);
    
    if (getResponse.statusCode === 404) {
      console.log('❌ FUNCTION NÃO DEPLOYADA');
      console.log('   A function stripe-portal não foi encontrada.');
      return false;
    } else if (getResponse.statusCode === 405) {
      console.log('✅ FUNCTION EXISTE (Method Not Allowed é esperado para GET)');
    } else {
      console.log(`⚠️  Status inesperado: ${getResponse.statusCode}`);
      console.log(`   Response: ${getResponse.body}`);
    }
  } catch (error) {
    console.log('❌ Erro na requisição GET:', error.message);
    return false;
  }

  console.log('\n🧪 Testando POST sem dados...');
  
  // Teste 2: POST sem dados (deve retornar erro de parâmetros)
  try {
    const postResponse = await makeRequest(PORTAL_URL, 'POST', {});
    console.log(`📊 POST sem dados - Status: ${postResponse.statusCode}`);
    console.log(`📄 Response: ${postResponse.body}`);
    
    if (postResponse.statusCode === 400) {
      const responseBody = JSON.parse(postResponse.body);
      if (responseBody.error && responseBody.error.includes('customer_id')) {
        console.log('✅ FUNCTION FUNCIONANDO CORRETAMENTE');
        console.log('   Erro esperado: Missing customer_id parameter');
      }
    }
  } catch (error) {
    console.log('❌ Erro na requisição POST:', error.message);
    return false;
  }

  console.log('\n🧪 Testando POST com customer_id inválido...');
  
  // Teste 3: POST com customer_id inválido (deve tentar criar portal)
  try {
    const testData = {
      customer_id: 'cus_test_invalid_customer',
      return_url: 'http://localhost:5173/subscription'
    };
    
    const postResponse = await makeRequest(PORTAL_URL, 'POST', testData);
    console.log(`📊 POST com dados - Status: ${postResponse.statusCode}`);
    console.log(`📄 Response: ${postResponse.body}`);
    
    if (postResponse.statusCode === 500) {
      console.log('✅ FUNCTION FUNCIONANDO CORRETAMENTE');
      console.log('   Status 500 é esperado com customer_id inválido');
      console.log('   A function está tentando criar o portal no Stripe');
    } else if (postResponse.statusCode === 200) {
      console.log('✅ FUNCTION FUNCIONANDO PERFEITAMENTE');
      console.log('   Retornou URL do portal com sucesso');
    }
  } catch (error) {
    console.log('❌ Erro na requisição POST com dados:', error.message);
    return false;
  }

  return true;
}

async function testOtherFunctions() {
  console.log('\n🔍 Verificando outras functions...');
  
  const functions = [
    'stripe-checkout',
    'stripe-webhook'
  ];
  
  for (const func of functions) {
    try {
      const url = `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/${func}`;
      const response = await makeRequest(url, 'GET');
      
      if (response.statusCode === 404) {
        console.log(`❌ ${func}: NÃO deployada`);
      } else {
        console.log(`✅ ${func}: Deployada (Status: ${response.statusCode})`);
      }
    } catch (error) {
      console.log(`❌ ${func}: Erro - ${error.message}`);
    }
  }
}

async function main() {
  const portalWorking = await testPortalFunction();
  await testOtherFunctions();
  
  console.log('\n🎯 RESUMO FINAL:');
  console.log('================');
  
  if (portalWorking) {
    console.log('✅ stripe-portal function está FUNCIONANDO!');
    console.log('✅ A function foi deployada com sucesso');
    console.log('✅ Está processando requisições corretamente');
  } else {
    console.log('❌ stripe-portal function NÃO está funcionando');
    console.log('📋 Você precisa fazer deploy:');
    console.log('   npx supabase functions deploy stripe-portal');
  }
  
  console.log('\n🔗 URL testada:');
  console.log(`   ${PORTAL_URL}`);
}

main().catch(console.error);
