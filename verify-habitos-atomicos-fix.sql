-- Verify "Hábitos Atômicos" Fix - Ensure Everything Works Correctly
-- Execute this SQL AFTER running fix-habitos-atomicos.sql

-- STEP 1: Verify only one version remains
SELECT 'VERIFICATION: REMAINING BOOKS' as step;

SELECT 
    COUNT(*) as total_books,
    CASE 
        WHEN COUNT(*) = 1 THEN '✅ SUCCESS: Only one version remains'
        WHEN COUNT(*) = 0 THEN '❌ ERROR: No books found'
        ELSE '⚠️ WARNING: Multiple versions still exist'
    END as status
FROM books 
WHERE LOWER(title) LIKE '%hábitos atômicos%' 
   OR LOWER(title) LIKE '%habitos atomicos%';

-- STEP 2: Check the remaining book details
SELECT 'VERIFICATION: BOOK DETAILS' as step;

SELECT 
    id,
    title,
    author,
    category,
    difficulty,
    duration,
    is_featured,
    pdf_key,
    CASE 
        WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN '✅ PDF_KEY CONFIGURED'
        ELSE '❌ NO PDF_KEY'
    END as pdf_status,
    CASE 
        WHEN is_featured THEN '✅ FEATURED'
        ELSE '⚠️ NOT FEATURED'
    END as featured_status,
    created_at
FROM books 
WHERE LOWER(title) LIKE '%hábitos atômicos%' 
   OR LOWER(title) LIKE '%habitos atomicos%';

-- STEP 3: Check for remaining placeholder content
SELECT 'VERIFICATION: CONTENT CHECK' as step;

SELECT 
    b.title,
    CASE 
        WHEN bc.content IS NULL THEN '✅ NO CONTENT (PDF will load)'
        WHEN bc.content::text LIKE '%conteúdo completo está sendo processado%' THEN '❌ PLACEHOLDER CONTENT'
        WHEN bc.content::text LIKE '%Nossa equipe está trabalhando%' THEN '❌ PLACEHOLDER CONTENT'
        ELSE '⚠️ HAS CONTENT (may interfere with PDF)'
    END as content_status,
    CASE 
        WHEN bc.content IS NOT NULL THEN LENGTH(bc.content::text)
        ELSE 0
    END as content_length
FROM books b
LEFT JOIN book_contents bc ON bc.book_id = b.id
WHERE LOWER(b.title) LIKE '%hábitos atômicos%' 
   OR LOWER(b.title) LIKE '%habitos atomicos%';

-- STEP 4: Check user progress data integrity
SELECT 'VERIFICATION: USER PROGRESS' as step;

SELECT 
    COUNT(*) as progress_records,
    COUNT(DISTINCT user_id) as unique_users,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ USER PROGRESS PRESERVED'
        ELSE '⚠️ NO USER PROGRESS DATA'
    END as status
FROM user_reading_progress urp
JOIN books b ON b.id::text = urp.book_id
WHERE LOWER(b.title) LIKE '%hábitos atômicos%' 
   OR LOWER(b.title) LIKE '%habitos atomicos%';

-- STEP 5: Test PDF accessibility (simulation)
SELECT 'VERIFICATION: PDF ACCESSIBILITY TEST' as step;

SELECT 
    title,
    pdf_key,
    CASE 
        WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 
            'https://qmeelujsnpbcdkzhcwmm.supabase.co/storage/v1/object/public/books/' || pdf_key
        ELSE 'NO PDF_KEY CONFIGURED'
    END as pdf_url,
    'Test this URL in browser to verify PDF exists' as instruction
FROM books 
WHERE LOWER(title) LIKE '%hábitos atômicos%' 
   OR LOWER(title) LIKE '%habitos atomicos%';

-- STEP 6: Check for orphaned data
SELECT 'VERIFICATION: ORPHANED DATA CHECK' as step;

-- Check for orphaned book_contents
SELECT 
    'Orphaned book_contents' as data_type,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ NO ORPHANED DATA'
        ELSE '⚠️ ORPHANED DATA FOUND'
    END as status
FROM book_contents bc
LEFT JOIN books b ON b.id = bc.book_id
WHERE b.id IS NULL
  AND bc.book_id IN (
      SELECT DISTINCT book_id::integer 
      FROM user_reading_progress 
      WHERE book_id ~ '^[0-9]+$'
  )

UNION ALL

-- Check for orphaned user_reading_progress
SELECT 
    'Orphaned user_reading_progress' as data_type,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ NO ORPHANED DATA'
        ELSE '⚠️ ORPHANED DATA FOUND'
    END as status
FROM user_reading_progress urp
LEFT JOIN books b ON b.id::text = urp.book_id
WHERE b.id IS NULL;

-- STEP 7: Verify book appears in featured section
SELECT 'VERIFICATION: FEATURED BOOKS' as step;

SELECT 
    title,
    category,
    is_featured,
    CASE 
        WHEN is_featured THEN '✅ WILL APPEAR IN FEATURED SECTION'
        ELSE '⚠️ NOT IN FEATURED SECTION'
    END as featured_status
FROM books 
WHERE LOWER(title) LIKE '%hábitos atômicos%' 
   OR LOWER(title) LIKE '%habitos atomicos%';

-- STEP 8: Check PDF loading flow simulation
SELECT 'VERIFICATION: PDF LOADING FLOW' as step;

WITH book_check AS (
    SELECT 
        id,
        title,
        pdf_key,
        CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 ELSE 0 END as has_pdf_key,
        CASE WHEN EXISTS (SELECT 1 FROM book_contents bc WHERE bc.book_id = books.id) THEN 1 ELSE 0 END as has_content
    FROM books 
    WHERE LOWER(title) LIKE '%hábitos atômicos%' 
       OR LOWER(title) LIKE '%habitos atomicos%'
)
SELECT 
    title,
    CASE 
        WHEN has_pdf_key = 1 AND has_content = 0 THEN '✅ WILL LOAD PDF (BookLoader.getBookText returns <PDF_VIEWER_PLACEHOLDER>)'
        WHEN has_pdf_key = 1 AND has_content = 1 THEN '⚠️ MAY LOAD CONTENT INSTEAD OF PDF'
        WHEN has_pdf_key = 0 AND has_content = 1 THEN '📄 WILL LOAD STRUCTURED CONTENT'
        ELSE '❌ NO CONTENT AVAILABLE'
    END as loading_behavior,
    CASE 
        WHEN has_pdf_key = 1 AND has_content = 0 THEN 'EdgeCompatiblePDFViewer will be used'
        WHEN has_content = 1 THEN 'Text content will be displayed'
        ELSE 'Error message will be shown'
    END as component_used
FROM book_check;

-- STEP 9: Final health check
SELECT 'FINAL HEALTH CHECK' as step;

WITH health_check AS (
    SELECT 
        COUNT(*) as book_count,
        COUNT(CASE WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN 1 END) as books_with_pdf,
        COUNT(CASE WHEN is_featured THEN 1 END) as featured_books,
        COUNT(CASE WHEN category = 'Desenvolvimento Pessoal' THEN 1 END) as correct_category
    FROM books 
    WHERE LOWER(title) LIKE '%hábitos atômicos%' 
       OR LOWER(title) LIKE '%habitos atomicos%'
)
SELECT 
    book_count,
    books_with_pdf,
    featured_books,
    correct_category,
    CASE 
        WHEN book_count = 1 AND books_with_pdf = 1 AND featured_books = 1 AND correct_category = 1 
        THEN '✅ PERFECT: All checks passed'
        WHEN book_count = 1 AND books_with_pdf = 1 
        THEN '✅ GOOD: Book configured correctly'
        WHEN book_count = 1 
        THEN '⚠️ PARTIAL: Book exists but needs PDF configuration'
        ELSE '❌ FAILED: Multiple issues detected'
    END as overall_status
FROM health_check;

-- STEP 10: Application testing instructions
SELECT 'APPLICATION TESTING INSTRUCTIONS' as step;

SELECT 
    '1. Clear application cache (click 🔄 Atualizar button)' as step_1,
    '2. Hard refresh browser (Ctrl+F5 or Cmd+Shift+R)' as step_2,
    '3. Navigate to Library section' as step_3,
    '4. Look for "Hábitos Atômicos" in featured books' as step_4,
    '5. Click "Ler Agora" button' as step_5,
    '6. Verify PDF loads in EdgeCompatiblePDFViewer' as step_6,
    '7. Test zoom, navigation, and reading progress' as step_7;

-- Final message
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM books WHERE LOWER(title) LIKE '%hábitos atômicos%' OR LOWER(title) LIKE '%habitos atomicos%') = 1
        THEN '✅ VERIFICATION COMPLETE: Hábitos Atômicos is ready for testing'
        ELSE '❌ VERIFICATION FAILED: Issues detected, review results above'
    END as final_status;
