/*
  # Add book_contents table for storing PDF content

  This migration adds the missing book_contents table that is referenced
  in the application code but was missing from the database schema.
*/

-- Create book_contents table if it doesn't exist
CREATE TABLE IF NOT EXISTS book_contents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  book_id integer NOT NULL REFERENCES books(id) ON DELETE CASCADE,
  content jsonb NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  UNIQUE(book_id)
);

-- Enable RLS on book_contents table
ALTER TABLE book_contents ENABLE ROW LEVEL SECURITY;

-- Create policies for book_contents table
DROP POLICY IF EXISTS "Allow public read access on book_contents" ON book_contents;
CREATE POLICY "Allow public read access on book_contents" ON book_contents FOR SELECT TO public USING (true);

DROP POLICY IF EXISTS "<PERSON><PERSON> can manage book_contents" ON book_contents;
CREATE POLICY "<PERSON><PERSON> can manage book_contents" ON book_contents FOR ALL TO authenticated USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'ADMIN')
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_book_contents_book_id ON book_contents(book_id);

-- Create trigger for updated_at column
DROP TRIGGER IF EXISTS update_book_contents_updated_at ON book_contents;
CREATE TRIGGER update_book_contents_updated_at BEFORE UPDATE ON book_contents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
