# 🚀 EXECUTE ESTES COMANDOS EXATOS

## ✅ **SUAS CHAVES CONFIGURADAS:**
- **Project ID:** qmeelujsnpbcdkzhcwmm
- **Service Role Key:** ✅ Configurada
- **Anon Key:** ✅ Configurada  
- **URL:** https://qmeelujsnpbcdkzhcwmm.supabase.co

---

## 📋 **COMANDOS PARA EXECUTAR (UM POR VEZ):**

### **1. Login no Supabase:**
```bash
npx supabase login
```
*Uma janela do navegador será aberta para login*

### **2. Link do Projeto:**
```bash
npx supabase link --project-ref qmeelujsnpbcdkzhcwmm
```

### **3. Configurar Stripe Secret Key:**
```bash
npx supabase secrets set STRIPE_SECRET_KEY="sk_test_51RIcDWQXmlk54RXco22WsOUYY2jtE27RhFyvbaaiInFpIDD5yFcbQNPjSilbv6pzKxq1YuihBgAy4ria1OV3l2mY00gw9ED5fy"
```

### **4. Configurar Stripe Webhook Secret:**
```bash
npx supabase secrets set STRIPE_WEBHOOK_SECRET="whsec_rSOSlhBwwbkRSVfgqla96EctF9NTcL9c"
```

### **5. Configurar Supabase URL:**
```bash
npx supabase secrets set SUPABASE_URL="https://qmeelujsnpbcdkzhcwmm.supabase.co"
```

### **6. Configurar Supabase Anon Key:**
```bash
npx supabase secrets set SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4"
```

### **7. Configurar Service Role Key:**
```bash
npx supabase secrets set SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjM2MjM0NSwiZXhwIjoyMDYxOTM4MzQ1fQ.zDusZDEOpQjEjgZhydao_ShbqddElyIA-rExxBj8yCM"
```

### **8. Verificar Secrets:**
```bash
npx supabase secrets list
```

---

## 🚀 **DEPLOY DAS EDGE FUNCTIONS:**

### **9. Deploy Stripe Checkout:**
```bash
npx supabase functions deploy stripe-checkout
```

### **10. Deploy Stripe Webhook:**
```bash
npx supabase functions deploy stripe-webhook
```

### **11. Deploy Stripe Portal:**
```bash
npx supabase functions deploy stripe-portal
```

### **12. Listar Functions:**
```bash
npx supabase functions list
```

---

## 📊 **VERIFICAR FUNCIONAMENTO:**

### **13. Ver Logs do Webhook:**
```bash
npx supabase functions logs stripe-webhook
```

### **14. Testar Checkout Function:**
```bash
curl -X POST https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-checkout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4" \
  -d '{
    "price_id": "price_1Rov58QXmlk54RXcFlW7ymIL",
    "user_id": "test-user-123",
    "success_url": "http://localhost:5173/subscription/success",
    "cancel_url": "http://localhost:5173/subscription/cancel",
    "plan_id": "premium"
  }'
```

---

## ✅ **RESULTADO ESPERADO:**

Após executar todos os comandos, você deve ter:

### **🔐 Secrets Configurados:**
- ✅ STRIPE_SECRET_KEY
- ✅ STRIPE_WEBHOOK_SECRET  
- ✅ SUPABASE_URL
- ✅ SUPABASE_ANON_KEY
- ✅ SUPABASE_SERVICE_ROLE_KEY

### **🚀 Functions Deployadas:**
- ✅ stripe-checkout
- ✅ stripe-webhook
- ✅ stripe-portal

### **🌐 URLs Funcionais:**
- ✅ `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-checkout`
- ✅ `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-webhook`
- ✅ `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal`

---

## 🎯 **WEBHOOK JÁ CONFIGURADO NO STRIPE:**
- **URL:** `https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-webhook`
- **Secret:** `whsec_rSOSlhBwwbkRSVfgqla96EctF9NTcL9c`
- **Eventos:** Todos os 45 eventos configurados ✅

---

## 🧪 **TESTAR NA APLICAÇÃO:**

1. **Inicie sua aplicação:**
   ```bash
   npm run dev
   ```

2. **Navegue para seção "Assinatura"**

3. **Tente fazer upgrade para Premium**

4. **Monitore logs em tempo real:**
   ```bash
   npx supabase functions logs stripe-webhook --follow
   ```

---

## 🎉 **PRONTO!**

Após executar todos os comandos acima, sua integração Stripe + Supabase estará **100% funcional**!

**Execute os comandos na ordem e me informe se algum der erro.** 🚀
