-- Quick check to see if the target books still exist in the database
-- Execute this in Supabase Dashboard > SQL Editor

SELECT 'CHECKING FOR REMOVED BOOKS' as status;

-- Check for "Pense e Enriqueça" books
SELECT 
    'Napoleon Hill Books Found' as book_type,
    COUNT(*) as count,
    STRING_AGG(CONCAT('ID:', id, ' - ', title), '; ') as books_found
FROM books 
WHERE LOWER(author) LIKE '%napoleon hill%'
   OR (LOWER(title) LIKE '%pense e enriqueça%' OR LOWER(title) LIKE '%pense e enriqueca%');

-- Check for "Gestalt-Terapia" books  
SELECT 
    'Gestalt-Terapia Books Found' as book_type,
    COUNT(*) as count,
    STRING_AGG(CONCAT('ID:', id, ' - ', title), '; ') as books_found
FROM books 
WHERE LOWER(title) LIKE '%gestalt%'
   AND (LOWER(title) LIKE '%terapia%' OR LOWER(author) LIKE '%perls%');

-- Show all books that match the removal criteria
SELECT 
    id,
    title,
    author,
    category,
    is_featured,
    created_at
FROM books 
WHERE (LOWER(author) LIKE '%napoleon hill%' OR 
       (LOWER(title) LIKE '%pense e enriqueça%' OR LOWER(title) LIKE '%pense e enriqueca%'))
   OR (LOWER(title) LIKE '%gestalt%' AND 
       (LOWER(title) LIKE '%terapia%' OR LOWER(author) LIKE '%perls%'))
ORDER BY id;

-- Check total books count
SELECT 'Total books in database' as info, COUNT(*) as count FROM books;

-- Check if there are any user progress records for non-existent books
SELECT 'Orphaned user progress records' as info, COUNT(*) as count
FROM user_reading_progress urp
WHERE NOT EXISTS (
    SELECT 1 FROM books b WHERE b.id::TEXT = urp.book_id
);
