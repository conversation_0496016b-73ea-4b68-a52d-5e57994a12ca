-- Corri<PERSON><PERSON> o livro "Um Apelo à Consciência" de <PERSON> Jr.
-- Execute este SQL no Supabase Dashboard > SQL Editor

-- STEP 1: Encontrar o livro específico
SELECT 'ENCONTRANDO O LIVRO' as step;

-- <PERSON>car o livro por título e autor
WITH target_book AS (
    SELECT 
        id,
        title,
        author,
        pdf_key,
        description
    FROM books 
    WHERE (LOWER(title) LIKE '%apelo%consciência%' 
           OR LOWER(title) LIKE '%apelo%consciencia%'
           OR (LOWER(title) LIKE '%martin luther king%' AND LOWER(title) LIKE '%discursos%')
           OR (LOWER(author) LIKE '%martin luther king%' AND LOWER(title) LIKE '%melhores%'))
    ORDER BY created_at DESC
    LIMIT 1
)
SELECT 
    id,
    title,
    author,
    pdf_key,
    CASE 
        WHEN pdf_key IS NOT NULL AND pdf_key != '' THEN '✅ TEM PDF_KEY'
        ELSE '❌ SEM PDF_KEY'
    END as current_status
FROM target_book;

-- STEP 2: Atualizar o pdf_key do livro com o caminho correto
UPDATE books 
SET 
    pdf_key = 'um_apelo_a_consciencia_os_melhores_discursos_martin_luther_king_3.pdf',
    updated_at = now()
WHERE (LOWER(title) LIKE '%apelo%consciência%' 
       OR LOWER(title) LIKE '%apelo%consciencia%'
       OR (LOWER(title) LIKE '%martin luther king%' AND LOWER(title) LIKE '%discursos%')
       OR (LOWER(author) LIKE '%martin luther king%' AND LOWER(title) LIKE '%melhores%'))
  AND id = (
      SELECT id FROM books 
      WHERE (LOWER(title) LIKE '%apelo%consciência%' 
             OR LOWER(title) LIKE '%apelo%consciencia%'
             OR (LOWER(title) LIKE '%martin luther king%' AND LOWER(title) LIKE '%discursos%')
             OR (LOWER(author) LIKE '%martin luther king%' AND LOWER(title) LIKE '%melhores%'))
      ORDER BY created_at DESC
      LIMIT 1
  );

-- STEP 3: Verificar se a atualização funcionou
SELECT 'VERIFICANDO ATUALIZAÇÃO' as step;

SELECT 
    id,
    title,
    author,
    pdf_key,
    CASE 
        WHEN pdf_key = 'um_apelo_a_consciencia_os_melhores_discursos_martin_luther_king_3.pdf' THEN '✅ PDF_KEY CORRIGIDO'
        ELSE '❌ PDF_KEY NÃO CORRIGIDO'
    END as fix_status,
    updated_at
FROM books 
WHERE (LOWER(title) LIKE '%apelo%consciência%' 
       OR LOWER(title) LIKE '%apelo%consciencia%'
       OR (LOWER(title) LIKE '%martin luther king%' AND LOWER(title) LIKE '%discursos%')
       OR (LOWER(author) LIKE '%martin luther king%' AND LOWER(title) LIKE '%melhores%'))
ORDER BY updated_at DESC;

-- STEP 4: Opcional - Adicionar entrada na tabela pdf_files se necessário
-- (Isso é um backup caso o sistema precise da tabela pdf_files)

DO $$
DECLARE
    book_record RECORD;
BEGIN
    -- Buscar o livro que acabamos de atualizar
    SELECT id, title INTO book_record
    FROM books 
    WHERE pdf_key = 'um_apelo_a_consciencia_os_melhores_discursos_martin_luther_king_3.pdf'
    LIMIT 1;
    
    IF book_record.id IS NOT NULL THEN
        -- Verificar se já existe entrada na pdf_files
        IF NOT EXISTS (
            SELECT 1 FROM pdf_files 
            WHERE book_id = book_record.id::text
        ) THEN
            -- Inserir entrada na pdf_files (sem file_data, apenas referência)
            INSERT INTO pdf_files (
                book_id,
                filename,
                file_size,
                created_at,
                updated_at
            ) VALUES (
                book_record.id::text,
                'um_apelo_a_consciencia_os_melhores_discursos_martin_luther_king_3.pdf',
                0, -- Tamanho será determinado pelo Storage
                now(),
                now()
            );
            
            RAISE NOTICE 'Entrada criada na tabela pdf_files para o livro: %', book_record.title;
        ELSE
            RAISE NOTICE 'Entrada já existe na tabela pdf_files para o livro: %', book_record.title;
        END IF;
    END IF;
END $$;

-- STEP 5: Limpar conteúdo placeholder da tabela book_contents (se existir)
-- Isso força o sistema a usar o PDF em vez do conteúdo placeholder

DELETE FROM book_contents 
WHERE book_id IN (
    SELECT id FROM books 
    WHERE pdf_key = 'um_apelo_a_consciencia_os_melhores_discursos_martin_luther_king_3.pdf'
);

-- STEP 6: Verificação final
SELECT 'VERIFICAÇÃO FINAL' as step;

SELECT 
    b.id,
    b.title,
    b.author,
    b.pdf_key,
    CASE 
        WHEN b.pdf_key = 'um_apelo_a_consciencia_os_melhores_discursos_martin_luther_king_3.pdf' THEN '✅ PDF_KEY CORRETO'
        ELSE '❌ PDF_KEY INCORRETO'
    END as pdf_key_status,
    CASE 
        WHEN pf.id IS NOT NULL THEN '✅ TEM ENTRADA PDF_FILES'
        ELSE '⚠️ SEM ENTRADA PDF_FILES'
    END as pdf_files_status,
    CASE 
        WHEN bc.id IS NOT NULL THEN '⚠️ TEM CONTEÚDO PLACEHOLDER'
        ELSE '✅ SEM CONTEÚDO PLACEHOLDER'
    END as content_status
FROM books b
LEFT JOIN pdf_files pf ON pf.book_id = b.id::text
LEFT JOIN book_contents bc ON bc.book_id = b.id
WHERE b.pdf_key = 'um_apelo_a_consciencia_os_melhores_discursos_martin_luther_king_3.pdf';

-- STEP 7: Testar acesso ao Storage (simulação)
SELECT 'TESTE DE ACESSO AO STORAGE' as step;

-- Mostrar a URL que o sistema tentará acessar
SELECT 
    'https://qmeelujsnpbcdkzhcwmm.supabase.co/storage/v1/object/public/books/' || pdf_key as public_url,
    'Teste esta URL no navegador para confirmar acesso' as instruction
FROM books 
WHERE pdf_key = 'um_apelo_a_consciencia_os_melhores_discursos_martin_luther_king_3.pdf';

-- Mensagem de sucesso
SELECT 
    '✅ CORREÇÃO APLICADA COM SUCESSO' as status,
    'O livro agora deve carregar o PDF corretamente' as message,
    'Teste abrindo o livro na aplicação' as next_step;
