const https = require('https');

console.log('🔍 TESTE DETALHADO - CACHE E VERSÃO');
console.log('===================================\n');

const PORTAL_URL = 'https://qmeelujsnpbcdkzhcwmm.supabase.co/functions/v1/stripe-portal';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';

function makeRequest(url, method = 'GET', data = null, extraHeaders = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname,
      method: method,
      headers: {
        'Authorization': `Bearer ${ANON_KEY}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Node.js Test Client',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        ...extraHeaders
      }
    };

    if (data && method === 'POST') {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: responseData
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data && method === 'POST') {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testMultipleTimes() {
  console.log('🧪 Testando múltiplas vezes para verificar cache...\n');
  
  for (let i = 1; i <= 3; i++) {
    console.log(`Teste ${i}:`);
    
    try {
      const response = await makeRequest(PORTAL_URL, 'POST', {}, {
        'X-Test-Request': `test-${i}-${Date.now()}`
      });
      
      console.log(`  Status: ${response.statusCode}`);
      console.log(`  Response: ${response.body}`);
      
      if (response.body.includes('Customer ID é obrigatório')) {
        console.log('  ❌ Português (versão antiga)');
      } else if (response.body.includes('Missing customer_id parameter')) {
        console.log('  ✅ Inglês (versão correta)');
      } else {
        console.log('  ❓ Resposta inesperada');
      }
      
      console.log('');
      
      // Aguardar 2 segundos entre testes
      if (i < 3) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
    } catch (error) {
      console.log(`  ❌ Erro: ${error.message}\n`);
    }
  }
}

async function testWithDifferentHeaders() {
  console.log('🔧 Testando com headers diferentes para bypass de cache...\n');
  
  const headerVariations = [
    { 'Cache-Control': 'no-cache, no-store, must-revalidate' },
    { 'Pragma': 'no-cache' },
    { 'Expires': '0' },
    { 'X-Requested-With': 'XMLHttpRequest' },
    { 'X-Force-Refresh': Date.now().toString() }
  ];
  
  for (let i = 0; i < headerVariations.length; i++) {
    console.log(`Teste com headers ${i + 1}:`);
    
    try {
      const response = await makeRequest(PORTAL_URL, 'POST', {}, headerVariations[i]);
      
      console.log(`  Status: ${response.statusCode}`);
      console.log(`  Response: ${response.body}`);
      
      if (response.body.includes('Missing customer_id parameter')) {
        console.log('  ✅ SUCESSO! Versão correta encontrada!');
        return true;
      } else if (response.body.includes('Customer ID é obrigatório')) {
        console.log('  ❌ Ainda versão antiga');
      }
      
      console.log('');
      
    } catch (error) {
      console.log(`  ❌ Erro: ${error.message}\n`);
    }
  }
  
  return false;
}

async function checkFunctionTimestamp() {
  console.log('⏰ Verificando timestamp da function...\n');
  
  try {
    // Fazer uma requisição GET para ver se retorna informações de debug
    const response = await makeRequest(PORTAL_URL, 'GET');
    
    console.log(`Status GET: ${response.statusCode}`);
    console.log(`Headers: ${JSON.stringify(response.headers, null, 2)}`);
    
    if (response.headers['last-modified']) {
      console.log(`Last-Modified: ${response.headers['last-modified']}`);
    }
    
    if (response.headers['etag']) {
      console.log(`ETag: ${response.headers['etag']}`);
    }
    
  } catch (error) {
    console.log(`Erro ao verificar timestamp: ${error.message}`);
  }
}

async function main() {
  await testMultipleTimes();
  
  const foundCorrectVersion = await testWithDifferentHeaders();
  
  await checkFunctionTimestamp();
  
  console.log('\n🎯 CONCLUSÃO:');
  console.log('=============');
  
  if (foundCorrectVersion) {
    console.log('✅ A versão correta foi encontrada com headers específicos!');
    console.log('   O deploy funcionou, mas pode haver cache.');
  } else {
    console.log('❌ A versão ainda está em português.');
    console.log('   Possíveis causas:');
    console.log('   1. Deploy não foi concluído');
    console.log('   2. Erro no processo de deploy');
    console.log('   3. Cache muito agressivo do Supabase');
    console.log('   4. Múltiplas versões da function');
  }
  
  console.log('\n📋 PRÓXIMOS PASSOS:');
  console.log('1. Aguardar mais alguns minutos');
  console.log('2. Tentar deploy novamente após login');
  console.log('3. Verificar no Dashboard do Supabase');
  console.log('4. Usar o Dashboard para deploy manual');
}

main().catch(console.error);
