import { createClient } from 'npm:@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

interface Database {
  public: {
    Tables: {
      user_reading_progress: {
        Row: {
          id: string
          user_id: string
          summary_id: string
          progress_percentage: number
          is_completed: boolean
          is_favorited: boolean
          last_read_at: string
          reading_time_minutes: number
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id: string
          summary_id: string
          progress_percentage?: number
          is_completed?: boolean
          is_favorited?: boolean
          last_read_at?: string
          reading_time_minutes?: number
        }
        Update: {
          progress_percentage?: number
          is_completed?: boolean
          is_favorited?: boolean
          last_read_at?: string
          reading_time_minutes?: number
          updated_at?: string
        }
      }
      user_subscriptions: {
        Row: {
          id: string
          user_id: string
          subscription_type: 'free' | 'premium'
          status: 'active' | 'cancelled' | 'expired'
          started_at: string
          expires_at: string | null
          remaining_free_access: number
          created_at: string
          updated_at: string
        }
      }
      summaries: {
        Row: {
          id: string
          book_id: number
          title: string
          content: string | null
          key_points: any[]
          word_count: number
          estimated_reading_time: number
          is_published: boolean
          created_at: string
          updated_at: string
        }
      }
      books: {
        Row: {
          id: number
          title: string
          author: string
          category: string
          pages: number
          duration: number
          description: string
          difficulty: string
          is_featured: boolean
          is_free: boolean
          pdf_key: string | null
          cover_image_url: string | null
          created_at: string
        }
      }
    }
  }
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { data: { user }, error: authError } = await supabaseClient.auth.getUser()
    
    if (authError || !user) {
      console.error('Authentication error:', authError)
      return new Response(
        JSON.stringify({ error: 'Authentication required', details: authError?.message }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('Authenticated user:', user.id)

    const url = new URL(req.url)
    const pathSegments = url.pathname.split('/').filter(Boolean)
    const apiPath = pathSegments.slice(3).join('/') // Remove /functions/v1/user-progress

    console.log('API Path:', apiPath, 'Method:', req.method)

    switch (req.method) {
      case 'GET':
        return await handleGetProgress(supabaseClient, user.id, apiPath, url.searchParams)
      case 'POST':
        return await handleUpdateProgress(supabaseClient, user.id, req)
      case 'PUT':
        return await handleToggleFavorite(supabaseClient, user.id, apiPath, req)
      default:
        return new Response(
          JSON.stringify({ error: 'Method not allowed' }),
          { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Error in user-progress function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function handleGetProgress(supabaseClient: any, userId: string, path: string, searchParams: URLSearchParams) {
  console.log('handleGetProgress called with path:', path)
  
  if (path === '' || path === 'dashboard') {
    console.log('Fetching dashboard data for user:', userId)
    
    // First, ensure user has a subscription record
    const { data: existingSubscription } = await supabaseClient
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (!existingSubscription) {
      console.log('Creating default subscription for user')
      await supabaseClient
        .from('user_subscriptions')
        .insert({
          user_id: userId,
          subscription_type: 'free',
          status: 'active',
          remaining_free_access: 5
        })
    }

    // Get user progress with related data
    const { data: progress, error } = await supabaseClient
      .from('user_reading_progress')
      .select(`
        *,
        summaries!inner (
          id,
          title,
          estimated_reading_time,
          books!inner (
            id,
            title,
            author,
            cover_image_url
          )
        )
      `)
      .eq('user_id', userId)
      .order('last_read_at', { ascending: false })

    if (error) {
      console.error('Error fetching progress:', error)
      // Return empty data instead of error for better UX
      const emptyDashboard = {
        recently_read: [],
        favorites: [],
        completed: [],
        in_progress: [],
        subscription: {
          subscription_type: 'free',
          status: 'active',
          remaining_free_access: 5
        },
        stats: {
          total_summaries_read: 0,
          total_reading_time: 0,
          favorites_count: 0,
          in_progress_count: 0
        },
        streak: { current: 0, longest: 0, this_week: 0 }
      }
      
      return new Response(
        JSON.stringify({ data: emptyDashboard }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('Progress data fetched:', progress?.length || 0, 'records')

    // Separate into different categories
    const recentlyRead = progress?.filter((p: any) => p.last_read_at).slice(0, 5) || []
    const favorites = progress?.filter((p: any) => p.is_favorited) || []
    const completed = progress?.filter((p: any) => p.is_completed) || []
    const inProgress = progress?.filter((p: any) => p.progress_percentage > 0 && !p.is_completed) || []

    // Get subscription info
    const { data: subscription } = await supabaseClient
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single()

    // Calculate stats
    const stats = {
      total_summaries_read: completed.length,
      total_reading_time: completed.reduce((acc: number, p: any) => 
        acc + (p.summaries?.estimated_reading_time || 20), 0),
      favorites_count: favorites.length,
      in_progress_count: inProgress.length
    }

    const dashboardData = {
      recently_read: recentlyRead,
      favorites,
      completed,
      in_progress: inProgress,
      subscription: subscription || {
        subscription_type: 'free',
        status: 'active',
        remaining_free_access: 5
      },
      stats,
      streak: { current: 0, longest: 0, this_week: 0 }
    }

    console.log('Returning dashboard data with counts:', {
      recently_read: dashboardData.recently_read.length,
      favorites: dashboardData.favorites.length,
      completed: dashboardData.completed.length,
      in_progress: dashboardData.in_progress.length
    })

    return new Response(
      JSON.stringify({ data: dashboardData }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  if (path.startsWith('summary/')) {
    const summaryId = path.split('/')[1]
    console.log('Fetching progress for summary:', summaryId)
    
    const { data: progress, error } = await supabaseClient
      .from('user_reading_progress')
      .select('*')
      .eq('user_id', userId)
      .eq('summary_id', summaryId)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching summary progress:', error)
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ data: progress || null }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ error: 'Invalid endpoint' }),
    { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleUpdateProgress(supabaseClient: any, userId: string, req: Request) {
  const body = await req.json()
  const { summary_id, progress_percentage, reading_time_minutes } = body

  console.log('Updating progress:', { summary_id, progress_percentage, reading_time_minutes })

  if (!summary_id || progress_percentage === undefined) {
    return new Response(
      JSON.stringify({ error: 'summary_id and progress_percentage are required' }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  const progressData = {
    user_id: userId,
    summary_id,
    progress_percentage: Math.min(100, Math.max(0, progress_percentage)),
    is_completed: progress_percentage >= 100,
    last_read_at: new Date().toISOString(),
    reading_time_minutes: reading_time_minutes || 0
  }

  const { data, error } = await supabaseClient
    .from('user_reading_progress')
    .upsert(progressData, {
      onConflict: 'user_id,summary_id'
    })
    .select()
    .single()

  if (error) {
    console.error('Error updating progress:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  console.log('Progress updated successfully:', data)

  return new Response(
    JSON.stringify({ data }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleToggleFavorite(supabaseClient: any, userId: string, path: string, req: Request) {
  const body = await req.json()
  const { is_favorited } = body

  // Extract summary_id from path (format: favorite/:id)
  const summaryId = path.split('/')[1]

  console.log('Toggling favorite:', { summaryId, is_favorited })

  if (!summaryId || is_favorited === undefined) {
    return new Response(
      JSON.stringify({ error: 'summary_id and is_favorited are required' }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  const favoriteData = {
    user_id: userId,
    summary_id: summaryId,
    is_favorited,
    last_read_at: new Date().toISOString()
  }

  const { data, error } = await supabaseClient
    .from('user_reading_progress')
    .upsert(favoriteData, {
      onConflict: 'user_id,summary_id'
    })
    .select()
    .single()

  if (error) {
    console.error('Error toggling favorite:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  console.log('Favorite toggled successfully:', data)

  return new Response(
    JSON.stringify({ data }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}
