import { supabase } from './supabase'

const API_BASE_URL = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1`

interface ApiResponse<T> {
  data: T
  error?: string
}

interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
  }
}

class ApiClient {
  private async getHeaders(): Promise<HeadersInit> {
    const { data: { session } } = await supabase.auth.getSession()
    const token = session?.access_token
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`
    const headers = await this.getHeaders()
    const response = await fetch(url, {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    })

    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.error || 'API request failed')
    }

    return data
  }

  // Summaries API
  async getSummaries(params?: {
    page?: number
    limit?: number
    category?: string
    search?: string
  }): Promise<PaginatedResponse<Summary>> {
    const searchParams = new URLSearchParams()
    if (params?.page) searchParams.set('page', params.page.toString())
    if (params?.limit) searchParams.set('limit', params.limit.toString())
    if (params?.category) searchParams.set('category', params.category)
    if (params?.search) searchParams.set('search', params.search)

    const query = searchParams.toString()
    return this.request(`/summaries${query ? `?${query}` : ''}`)
  }

  async getSummary(id: string): Promise<ApiResponse<Summary>> {
    return this.request(`/summaries/${id}`)
  }

  async getCategories(): Promise<ApiResponse<Category[]>> {
    return this.request('/summaries/categories')
  }

  async getFreeMonthlyS(): Promise<ApiResponse<MonthlySummary[]>> {
    return this.request('/summaries/free-monthly')
  }

  // User Progress API
  async getDashboard(): Promise<ApiResponse<UserDashboard>> {
    return this.request('/user-progress/dashboard')
  }

  async getSummaryProgress(summaryId: string): Promise<ApiResponse<UserProgress | null>> {
    return this.request(`/user-progress/summary/${summaryId}`)
  }

  async updateProgress(summaryId: string, progressPercentage: number): Promise<ApiResponse<UserProgress>> {
    return this.request('/user-progress', {
      method: 'POST',
      body: JSON.stringify({
        summary_id: summaryId,
        progress_percentage: progressPercentage,
      }),
    })
  }

  async toggleFavorite(summaryId: string, isFavorited: boolean): Promise<ApiResponse<UserProgress>> {
    return this.request(`/user-progress/favorite/${summaryId}`, {
      method: 'PUT',
      body: JSON.stringify({
        is_favorited: isFavorited,
      }),
    })
  }

  // Subscriptions API
  async getCurrentSubscription(): Promise<ApiResponse<UserSubscription>> {
    return this.request('/subscriptions/current')
  }

  async createCheckoutSession(priceId: string, successUrl?: string, cancelUrl?: string): Promise<ApiResponse<{ checkout_url: string }>> {
    return this.request('/subscriptions/create-checkout-session', {
      method: 'POST',
      body: JSON.stringify({
        price_id: priceId,
        success_url: successUrl,
        cancel_url: cancelUrl,
      }),
    })
  }

  async cancelSubscription(): Promise<ApiResponse<UserSubscription>> {
    return this.request('/subscriptions/cancel', {
      method: 'DELETE',
    })
  }
}

// Types
export interface Category {
  id: string
  name: string
  slug: string
  description?: string
}

export interface Book {
  id: string
  title: string
  author: string
  cover_image_url?: string
  reading_time_minutes: number
  categories: Category
}

export interface Summary {
  id: string
  title: string
  content?: string
  key_points: string[]
  word_count: number
  estimated_reading_time: number
  has_access: boolean
  books: Book
  created_at: string
}

export interface MonthlySummary {
  id: string
  month_year: string
  selection_order: number
  summaries: Summary
}

export interface UserSubscription {
  id: string
  user_id: string
  subscription_type: 'free' | 'premium'
  status: 'active' | 'cancelled' | 'expired'
  started_at: string
  expires_at?: string
  remaining_free_access: number
}

export interface UserProgress {
  id: string
  user_id: string
  summary_id: string
  progress_percentage: number
  is_completed: boolean
  is_favorited: boolean
  last_read_at: string
}

export interface UserDashboard {
  recently_read: (UserProgress & { summaries: Summary })[]
  favorites: (UserProgress & { summaries: Summary })[]
  completed: (UserProgress & { summaries: Summary })[]
  in_progress: (UserProgress & { summaries: Summary })[]
  subscription: UserSubscription
  stats: {
    total_summaries_read: number
    total_reading_time: number
    favorites_count: number
    in_progress_count: number
  }
}

export const api = new ApiClient()